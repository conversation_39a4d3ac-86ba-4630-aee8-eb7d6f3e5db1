# Import streamlit first before any other imports that might use it
import streamlit as st
import pandas as pd
import os
import tempfile
import shutil
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns
import base64
import warnings

# Disable specific warnings
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=FutureWarning)

# Disable Streamlit's file watcher for problematic modules
os.environ["STREAMLIT_WATCH_MODULES"] = "false"
os.environ["STREAMLIT_DISABLE_WATCHER"] = "true"

# Set page configuration
st.set_page_config(
    page_title="DIT Exam AI Marking System",
    
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state variables if they don't exist
if 'questions' not in st.session_state:
    st.session_state.questions = {}
if 'marking_scheme' not in st.session_state:
    st.session_state.marking_scheme = {}
if 'model' not in st.session_state:
    st.session_state.model = None
if 'results' not in st.session_state:
    st.session_state.results = None

# Import SentenceTransformer
try:
    from sentence_transformers import SentenceTransformer
except ImportError:
    st.error("Required libraries not found. Please install sentence-transformers.")
    st.stop()

# Define paths
CURRENT_DIR = Path(__file__).parent.absolute()
BOOKLET_FOLDER = CURRENT_DIR / "exam_booklets"
RESULTS_FILE = CURRENT_DIR / "exam_results.csv"
TRAINING_DATA = CURRENT_DIR / "training_data.csv"
FINE_TUNED_MODEL_PATH = CURRENT_DIR / "fine_tuned_model"
QUESTIONS_FILE = CURRENT_DIR / "questions.csv"
MARKING_SCHEME_FILE = CURRENT_DIR / "marking_scheme.csv"

# Create directories if they don't exist
BOOKLET_FOLDER.mkdir(parents=True, exist_ok=True)
FINE_TUNED_MODEL_PATH.mkdir(parents=True, exist_ok=True)

# Now import our modules after st is defined
from examAImarking import (
    load_config, fine_tune_model, load_model,
    extract_text, extract_answers_from_text,
    evaluate_written_answer, evaluate_mcq,
    cleanup_old_models
)

# Load configuration
CONFIG = load_config()

# Helper functions
def save_uploaded_file(uploaded_file, destination_folder):
    """Save an uploaded file to the specified folder"""
    # Create destination folder if it doesn't exist
    if isinstance(destination_folder, str):
        destination_folder = Path(destination_folder)
    destination_folder.mkdir(parents=True, exist_ok=True)
    
    # Create a temporary file
    with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
        # Write the uploaded file to the temporary file
        shutil.copyfileobj(uploaded_file, tmp_file)
        
    # Move the temporary file to the destination folder
    dest_path = destination_folder / uploaded_file.name
    shutil.move(tmp_file.name, dest_path)
    
    return str(dest_path)

def get_csv_download_link(df, filename, link_text):
    """Generate a link to download a dataframe as a CSV file"""
    csv = df.to_csv(index=False)
    b64 = base64.b64encode(csv.encode()).decode()
    href = f'<a href="data:file/csv;base64,{b64}" download="{filename}">{link_text}</a>'
    return href

def get_image_download_link(fig, filename, link_text):
    """Generate a link to download a matplotlib figure as a PNG file"""
    # Use BytesIO instead of a temporary file to avoid permission issues
    import io
    buf = io.BytesIO()
    fig.savefig(buf, format='png', dpi=300, bbox_inches='tight')
    buf.seek(0)
    img_data = buf.getvalue()
    b64 = base64.b64encode(img_data).decode()
    href = f'<a href="data:image/png;base64,{b64}" download="{filename}">{link_text}</a>'
    return href

def plot_results(df):
    """Create plots for the results"""
    # Get score columns (those starting with Q)
    score_cols = [col for col in df.columns if col.startswith('Q')]

    # Plot final scores by student (use Final_Score_Percentage if available, otherwise calculate)
    fig1, ax1 = plt.subplots(figsize=(12, 6))
    if 'Final_Score_Percentage' in df.columns:
        sns.barplot(x='Student', y='Final_Score_Percentage', data=df, ax=ax1)
        ax1.set_title('Final Scores by Student (%)')
        ax1.set_ylabel('Final Score (%)')
        ax1.set_ylim(0, 100)
        # Add score labels on bars
        for i, v in enumerate(df['Final_Score_Percentage']):
            ax1.text(i, v + 1, f'{v:.1f}%', ha='center', va='bottom')
    else:
        # Fallback to calculated average if Final_Score_Percentage not available
        num_questions = len(score_cols)
        total_possible = num_questions * 100
        df['Final_Score_Calculated'] = (df[score_cols].sum(axis=1) / total_possible) * 100
        sns.barplot(x='Student', y='Final_Score_Calculated', data=df, ax=ax1)
        ax1.set_title('Final Scores by Student (%) - Calculated')
        ax1.set_ylabel('Final Score (%)')
        ax1.set_ylim(0, 100)
        # Add score labels on bars
        for i, v in enumerate(df['Final_Score_Calculated']):
            ax1.text(i, v + 1, f'{v:.1f}%', ha='center', va='bottom')

    ax1.set_xticklabels(ax1.get_xticklabels(), rotation=45)
    plt.tight_layout()

    # Plot scores by question
    fig2, ax2 = plt.subplots(figsize=(12, 8))
    df_melted = df.melt(id_vars=['Student'], value_vars=score_cols, var_name='Question', value_name='Score')
    sns.boxplot(x='Question', y='Score', data=df_melted, ax=ax2)
    ax2.set_title('Score Distribution by Question')
    plt.tight_layout()

    return fig1, fig2

def process_student_answers(model):
    """Process student answers and return results"""
    results = []
    
    # Create a container for the processing log
    log_container = st.expander("Processing Log (Click to expand/collapse)", expanded=False)
    
    # Get list of valid booklet files
    booklet_files = [f for f in os.listdir(BOOKLET_FOLDER) 
                    if f.lower().endswith((".jpg", ".png", ".jpeg", ".tif", ".pdf"))]
    
    # Check if there are any files to process
    if not booklet_files:
        st.warning("No valid student answer booklets found in the booklet folder.")
        return None
    
    # Create a progress bar
    total_files = len(booklet_files)
    progress_bar = st.progress(0)
    status_text = st.empty()
    
    # Create a container for the summary
    summary_container = st.container()
    with summary_container:
        st.subheader("Processing Summary")
        summary_table = st.empty()
        summary_data = {"Student": [], "Status": [], "Average Score": []}
    
    # Log the start of processing
    with log_container:
        st.info(f"Starting to process {total_files} student booklets...")
    
    file_count = 0
    for file in booklet_files:
        file_count += 1
        file_path = os.path.join(BOOKLET_FOLDER, file)
        
        # Update progress
        status_text.text(f"Processing {file} ({file_count}/{total_files})...")
        progress_bar.progress(file_count / total_files)
        
        # Log the current file being processed
        with log_container:
            st.markdown(f"### Processing {file}")
        
        try:
            # Check if file exists and is readable
            if not os.path.exists(file_path):
                with log_container:
                    st.error(f"File not found: {file_path}")
                continue
                
            # Extract text and answers
            with log_container:
                st.text(f"Extracting text from {file}...")
            
            text = extract_text(os.path.join(BOOKLET_FOLDER, file))
            
            if not text or text.strip() == "":
                with log_container:
                    st.error(f"No text extracted from {file}")
                
                # Update summary
                summary_data["Student"].append(file)
                summary_data["Status"].append("❌ Failed (No text extracted)")
                summary_data["Average Score"].append("N/A")
                summary_table.dataframe(pd.DataFrame(summary_data))
                continue
            
            # Use a details HTML tag instead of an expander
            with log_container:
                st.markdown(f"""
                <details>
                <summary>Extracted text from {file}</summary>
                <pre>{text}</pre>
                </details>
                """, unsafe_allow_html=True)
            
            answers = extract_answers_from_text(text)
            
            if not answers:
                with log_container:
                    st.error(f"No answers extracted from {file}")
                
                # Update summary
                summary_data["Student"].append(file)
                summary_data["Status"].append("❌ Failed (No answers extracted)")
                summary_data["Average Score"].append("N/A")
                summary_table.dataframe(pd.DataFrame(summary_data))
                continue
            
            # Use a details HTML tag instead of an expander
            with log_container:
                answer_text = "\n".join([f"{q_id}: {ans}" for q_id, ans in answers.items()])
                st.markdown(f"""
                <details>
                <summary>Extracted answers from {file}</summary>
                <pre>{answer_text}</pre>
                </details>
                """, unsafe_allow_html=True)
            
            # Process scores
            scores = {}
            for q_id, ans in answers.items():
                if q_id in st.session_state.questions:
                    if st.session_state.questions[q_id]['type'] == 'written':
                        if q_id in st.session_state.marking_scheme:
                            model_ans = str(st.session_state.marking_scheme[q_id])
                            scores[q_id] = evaluate_written_answer(ans, model_ans, model)
                            
                            # Log in container
                            with log_container:
                                st.text(f"Written question {q_id} score: {scores[q_id]}")
                        else:
                            scores[q_id] = 0.0
                            with log_container:
                                st.warning(f"No marking scheme found for {q_id}")
                    else:  # MCQ
                        if q_id in st.session_state.marking_scheme:
                            correct_ans = str(st.session_state.marking_scheme[q_id])
                            scores[q_id] = evaluate_mcq(ans, correct_ans)
                            
                            # Log in container
                            with log_container:
                                st.text(f"MCQ {q_id} - Student: '{ans}', Correct: '{correct_ans}', Score: {scores[q_id]}")
                        else:
                            scores[q_id] = 0.0
                            with log_container:
                                st.warning(f"No marking scheme found for {q_id}")
                else:
                    scores[q_id] = 0.0
                    with log_container:
                        st.warning(f"Unknown question ID: {q_id}")
            
            # Calculate final score as percentage dynamically
            # Each question is worth 100 points, so total possible = number of questions * 100
            num_questions = len(scores)
            total_possible_points = num_questions * 100
            total_score = sum(scores.values())
            final_score_percentage = (total_score / total_possible_points) * 100 if total_possible_points > 0 else 0

            # Calculate average score for display
            score_values = [v for v in scores.values() if isinstance(v, (int, float))]
            avg_score = sum(score_values) / len(score_values) if score_values else 0

            # Create result row with Final_Score_Percentage right after the question scores
            result_row = {"Student": file}
            result_row.update(scores)  # Add all question scores
            result_row["Final_Score_Percentage"] = round(final_score_percentage, 2)

            # Add to results
            results.append(result_row)
            
            # Update summary
            summary_data["Student"].append(file)
            summary_data["Status"].append("✅ Success")
            summary_data["Average Score"].append(f"{avg_score:.2f}")
            
            # Update summary table
            summary_table.dataframe(pd.DataFrame(summary_data))
            
            # Log success
            with log_container:
                st.success(f"Successfully processed {file}")
                
        except Exception as e:
            # Log error
            with log_container:
                st.error(f"Failed to process {file}: {str(e)}")
                import traceback
                st.markdown(f"""
                <details>
                <summary>Error details</summary>
                <pre>{traceback.format_exc()}</pre>
                </details>
                """, unsafe_allow_html=True)
            
            # Update summary
            summary_data["Student"].append(file)
            summary_data["Status"].append(f"❌ Failed ({str(e)[:50]}...)")
            summary_data["Average Score"].append("N/A")
            
            # Update summary table
            summary_table.dataframe(pd.DataFrame(summary_data))
    
    # Clear progress indicators
    progress_bar.empty()
    status_text.empty()
    
    # Final summary
    if results:
        with log_container:
            st.success(f"Successfully processed {len(results)} out of {total_files} booklets.")
        return pd.DataFrame(results)
    else:
        with log_container:
            st.error("No booklets were successfully processed.")
        return None

# Load or train model
def load_or_train_model():
    """Load existing model or train a new one"""
    try:
        # First check if we have a fine-tuned model
        if os.path.exists(FINE_TUNED_MODEL_PATH) and os.listdir(FINE_TUNED_MODEL_PATH):
            st.info("Loading fine-tuned model...")
            try:
                model = load_model(FINE_TUNED_MODEL_PATH)
                st.success("Model loaded successfully!")
                return model
            except Exception as e:
                st.error(f"Error loading fine-tuned model: {e}")
                st.info("Falling back to default model...")
        else:
            st.info("No fine-tuned model found.")
            
        # Check if training data exists for fine-tuning
        if os.path.exists(TRAINING_DATA):
            st.info("Training data found. Fine-tuning model...")
            try:
                model = fine_tune_model(TRAINING_DATA)
                st.success("Model trained and saved successfully!")
                return model
            except Exception as e:
                st.error(f"Error fine-tuning model: {e}")
                st.info("Falling back to default model...")
        else:
            st.warning("No training data found. Using default model.")
        
        # If we get here, use the default model
        st.info("Loading default model (all-MiniLM-L6-v2)...")
        with st.spinner("Loading default model..."):
            model = SentenceTransformer("all-MiniLM-L6-v2")
            st.success("Default model loaded successfully!")
            return model
            
    except Exception as e:
        st.error(f"Error loading/training model: {e}")
        st.info("Using default model as last resort.")
        return SentenceTransformer("all-MiniLM-L6-v2")

# Sidebar
st.sidebar.title("DIT Exam AI Marking System")
#st.sidebar.image("https://img.icons8.com/color/96/000000/test-passed.png", width=100)

# Check environment
def check_environment():
    """Check for common issues that might cause warnings - console output only"""
    # Check Python version
    import platform
    python_version = platform.python_version()
    print(f"[INFO] Python version: {python_version}")

    # Check Streamlit version
    streamlit_version = st.__version__
    if streamlit_version < "1.10.0":
        print(f"[WARNING] Streamlit version {streamlit_version} is older than recommended (1.10.0+)")

    # Check for required libraries
    try:
        import sentence_transformers
        print(f"✅ SentenceTransformer installed (version: {sentence_transformers.__version__})")

        # Verify SentenceTransformer functionality
        try:
            print("[INFO] Verifying SentenceTransformer...")
            model = SentenceTransformer("all-MiniLM-L6-v2")
            test_embedding = model.encode("This is a test sentence.")
            if len(test_embedding) > 0:
                print("✅ SentenceTransformer is working properly")
        except Exception as e:
            print(f"❌ SentenceTransformer error: {str(e)[:50]}...")
            print("Try reinstalling with: pip install -U sentence-transformers")
    except ImportError:
        print("❌ SentenceTransformer not installed")
        print("Install with: pip install sentence-transformers")

    try:
        import torch
        print(f"✅ PyTorch installed (version: {torch.__version__})")
        if torch.cuda.is_available():
            print(f"✅ CUDA available (version: {torch.version.cuda})")
        else:
            print("ℹ️ CUDA not available, using CPU")
    except ImportError:
        print("❌ PyTorch not installed")
        print("Install with: pip install torch")

    # Check for required directories
    from pathlib import Path
    CURRENT_DIR = Path(__file__).parent.absolute()
    BOOKLET_FOLDER = CURRENT_DIR / "exam_booklets"
    FINE_TUNED_MODEL_PATH = CURRENT_DIR / "fine_tuned_model"

    if not BOOKLET_FOLDER.exists():
        BOOKLET_FOLDER.mkdir(parents=True, exist_ok=True)
        print(f"[INFO] Created booklet folder: {BOOKLET_FOLDER}")

    if not FINE_TUNED_MODEL_PATH.exists():
        FINE_TUNED_MODEL_PATH.mkdir(parents=True, exist_ok=True)
        print(f"[INFO] Created model folder: {FINE_TUNED_MODEL_PATH}")

    # Suppress specific warnings
    import warnings
    warnings.filterwarnings("ignore", category=UserWarning)
    warnings.filterwarnings("ignore", category=FutureWarning)
    warnings.filterwarnings("ignore", message=".*sentence-transformers model found with name.*")

# Run environment check
check_environment()

# Load model
model = load_or_train_model()
st.session_state.model = model

# Main navigation
page = st.sidebar.radio("Navigation", ["Upload Files", "Train Model", "Mark Exams", "View Results"])

# Upload Files Page
if page == "Upload Files":
    st.title("Upload Files")
    

    # Upload Questions
    st.header("1. Upload Questions")
    questions_file = st.file_uploader("Upload questions CSV file", type=["csv"], key="questions_uploader")
    
    if questions_file:
        try:
            # Save the uploaded file
            questions_path = save_uploaded_file(questions_file, ".")
            
            # Load questions
            df_questions = pd.read_csv(questions_path)
            st.dataframe(df_questions)
            
            # Store questions in session state
            questions = {}
            for _, row in df_questions.iterrows():
                q_id = row['question_id']
                q_type = row['question_type']
                q_text = row['question_text']
                questions[q_id] = {'type': q_type, 'text': q_text}
            
            st.session_state.questions = questions
            st.success(f"Successfully loaded {len(questions)} questions!")
            
        except Exception as e:
            st.error(f"Error loading questions: {str(e)}")
    
    # Upload Marking Scheme
    st.header("2. Upload Marking Scheme")
    marking_scheme_file = st.file_uploader("Upload marking scheme CSV file", type=["csv"], key="marking_scheme_uploader")
    
    if marking_scheme_file:
        try:
            # Save the uploaded file
            marking_scheme_path = save_uploaded_file(marking_scheme_file, ".")
            
            # Load marking scheme
            df_marking = pd.read_csv(marking_scheme_path)
            st.dataframe(df_marking)
            
            # Store marking scheme in session state
            marking_scheme = {}
            for _, row in df_marking.iterrows():
                q_id = row['question_id']
                if 'model_answer' in row and not pd.isna(row['model_answer']):
                    marking_scheme[q_id] = str(row['model_answer'])
                elif 'correct_option' in row and not pd.isna(row['correct_option']):
                    marking_scheme[q_id] = str(row['correct_option'])
            
            st.session_state.marking_scheme = marking_scheme
            st.success(f"Successfully loaded marking scheme!")
            
        except Exception as e:
            st.error(f"Error loading marking scheme: {str(e)}")
    
    # Upload Training Data
    st.header("3. Upload Training Data (Optional)")
    training_data_file = st.file_uploader("Upload training data CSV file", type=["csv"], key="training_data_uploader")
    
    if training_data_file:
        try:
            # Save the uploaded file
            training_data_path = save_uploaded_file(training_data_file, str(TRAINING_DATA.parent))
            
            # Rename to match expected filename
            os.rename(training_data_path, TRAINING_DATA)
            
            # Load and display training data
            df_training = pd.read_csv(TRAINING_DATA, on_bad_lines='skip')
            st.dataframe(df_training, use_container_width=True)
            
            st.success(f"Successfully loaded training data with {len(df_training)} examples!")
            
        except Exception as e:
            st.error(f"Error loading training data: {str(e)}")
    
    # Upload Student Answer Booklets
    st.header("4. Upload Student Answer Booklets")
    student_files = st.file_uploader("Upload student answer images", type=["jpg", "jpeg", "png", "pdf", "tif"], accept_multiple_files=True)
    
    if student_files:
        # Clear existing files
        if st.checkbox("Clear existing booklets before uploading new ones"):
            for file in os.listdir(BOOKLET_FOLDER):
                os.remove(BOOKLET_FOLDER / file)
            st.info("Cleared existing booklets.")
        
        # Save uploaded files
        for uploaded_file in student_files:
            save_uploaded_file(uploaded_file, BOOKLET_FOLDER)
        
        st.success(f"Successfully uploaded {len(student_files)} student answer booklets!")
        
        # Display uploaded files
        st.subheader("Uploaded Booklets")
        col_count = 4
        cols = st.columns(col_count)
        for i, file in enumerate(os.listdir(BOOKLET_FOLDER)):
            if file.lower().endswith((".jpg", ".png", ".jpeg")):
                cols[i % col_count].image(str(BOOKLET_FOLDER / file), caption=file, use_column_width=True)

# Train Model Page
if page == "Train Model":
    st.title("Train Model")
    
    # Upload Training Data
    st.header("Upload Training Data")
    training_data_file = st.file_uploader("Upload training data CSV file", type=["csv"], key="training_data_uploader")
    
    if training_data_file:
        try:
            # Save the uploaded file
            with open(TRAINING_DATA, "wb") as f:
                f.write(training_data_file.getbuffer())
            
            # Load and display training data
            df_training = pd.read_csv(TRAINING_DATA, on_bad_lines='skip')
            st.dataframe(df_training, use_container_width=True)
            
            st.success(f"Successfully loaded training data with {len(df_training)} examples!")
            
        except Exception as e:
            st.error(f"Error loading training data: {str(e)}")
    
    # Create Sample Training Data
    #st.header("Create Sample Training Data")
    #num_examples = st.slider("Number of examples", min_value=10, max_value=100, value=20, step=10)
    
    #if st.button("Generate Sample Training Data"):
      #  with st.spinner("Generating sample training data..."):
       #     try:
         #       from create_samples import create_sample_training_data
          #      create_sample_training_data(TRAINING_DATA, num_examples)
                
                # Load and display the generated data
             #   df_training = pd.read_csv(TRAINING_DATA)
              # st.dataframe(df_training)
                
               # st.success(f"Successfully generated {len(df_training)} training examples!")
                
            #except Exception as e:
            #    st.error(f"Error generating training data: {str(e)}")
    
    # Train Model Button
    st.header("Train Model")
    
    # Advanced training options
    with st.expander("Advanced Training Options", expanded=False):
        epochs = st.slider("Number of epochs", min_value=1, max_value=10, value=int(CONFIG['training']['epochs']))
        batch_size = st.slider("Batch size", min_value=8, max_value=64, value=int(CONFIG['training']['batch_size']))
        warmup_steps = st.slider("Warmup steps", min_value=0, max_value=500, value=int(CONFIG['training']['warmup_steps']))
        
        # Update config
        CONFIG['training']['epochs'] = str(epochs)
        CONFIG['training']['batch_size'] = str(batch_size)
        CONFIG['training']['warmup_steps'] = str(warmup_steps)
    
    if st.button("Train Model"):
        if not os.path.exists(TRAINING_DATA):
            st.error("No training data found. Please upload or generate training data first.")
        else:
            with st.spinner("Training model..."):
                try:
                    # Clean up old models before training
                    cleanup_old_models(keep_latest=3)
                    
                    # Train the model
                    model = fine_tune_model(TRAINING_DATA)
                    st.session_state.model = model
                    st.success("Model trained successfully!")
                    
                    # Show model info
                    st.info("Model information:")
                    st.json({
                        "model_type": "SentenceTransformer",
                        "base_model": "all-MiniLM-L6-v2",
                        "embedding_dimension": 384,
                        "training_examples": len(pd.read_csv(TRAINING_DATA)),
                        "epochs": epochs,
                        "batch_size": batch_size
                    })
                    
                except Exception as e:
                    st.error(f"Error training model: {str(e)}")
                    import traceback
                    st.code(traceback.format_exc())

# Mark Exams Page
elif page == "Mark Exams":
    st.title("Mark Exams")
    
    # Check if questions and marking scheme are loaded
    if not st.session_state.questions or not st.session_state.marking_scheme:
        st.warning("Please upload questions and marking scheme first!")
        st.stop()
    
    # Check if student booklets are uploaded
    booklet_files = [f for f in os.listdir(BOOKLET_FOLDER) if f.lower().endswith((".jpg", ".png", ".jpeg", ".tif", ".pdf"))]
    if not booklet_files:
        st.warning("Please upload student answer booklets first!")
        st.stop()
    
    # Display information
    st.info(f"Found {len(booklet_files)} student answer booklets.")
    
    # Display questions and marking scheme
    with st.expander("View Questions and Marking Scheme", expanded=False):
        st.subheader("Questions")
        for q_id, q_info in st.session_state.questions.items():
            st.markdown(f"**{q_id}** ({q_info['type']}): {q_info['text']}")
        
        st.subheader("Marking Scheme")
        for q_id, answer in st.session_state.marking_scheme.items():
            st.markdown(f"**{q_id}**: {answer}")
    
    # Mark exams button
    if st.session_state.model is not None:
        if st.button("Mark Exams"):
            with st.spinner("Initializing marking process..."):
                try:
                    # Process student answers
                    results = process_student_answers(st.session_state.model)
                    
                    if results is not None and not results.empty:
                        # Save results
                        results.to_csv(RESULTS_FILE, index=False)
                        st.session_state.results = results
                        
                        st.success("Exams marked successfully!")
                        
                        # Display results
                        st.subheader("Results")
                        st.dataframe(results, use_container_width=True)
                        
                        # Download link
                        st.markdown(get_csv_download_link(results, "exam_results.csv", "Download Results CSV"), unsafe_allow_html=True)
                        
                        # Redirect to results page
                        st.info("You can view detailed results and visualizations in the 'View Results' page.")
                    else:
                        st.warning("No results were generated. Please check the student answer booklets.")
                        
                except Exception as e:
                    st.error(f"Error marking exams: {str(e)}")
    else:
        st.warning("Please train or load a model first!")

# View Results Page
elif page == "View Results":
    st.title("View Results")
    
    # Check if results exist
    if os.path.exists(RESULTS_FILE):
        # Load results
        results = pd.read_csv(RESULTS_FILE)
        st.session_state.results = results
        
        # Display results
        st.subheader("Exam Results")
        st.dataframe(results)
        
        # Download link
        st.markdown(get_csv_download_link(results, "exam_results.csv", "Download Results CSV"), unsafe_allow_html=True)
        
        # Create plots
        fig1, fig2 = plot_results(results)
        
        if fig1 and fig2:
            # Display plots
            st.subheader("Average Scores by Student")
            fig1.tight_layout()  # Ensure proper layout
            st.pyplot(fig1, clear_figure=True)  # Clear figure after displaying
            st.markdown(get_image_download_link(fig1, "average_scores.png", "Download Average Scores Plot"), unsafe_allow_html=True)

            st.subheader("Score Distribution by Question")
            fig2.tight_layout()  # Ensure proper layout
            st.pyplot(fig2, clear_figure=True)  # Clear figure after displaying
            st.markdown(get_image_download_link(fig2, "question_scores.png", "Download Question Scores Plot"), unsafe_allow_html=True)

            # Calculate statistics
            score_cols = [col for col in results.columns if col.startswith('Q')]

            # Use Final_Score_Percentage if available, otherwise calculate it
            if 'Final_Score_Percentage' in results.columns:
                final_scores = results['Final_Score_Percentage']
                score_label = "Final Score (%)"
            else:
                # Calculate final scores dynamically
                num_questions = len(score_cols)
                total_possible = num_questions * 100
                final_scores = (results[score_cols].sum(axis=1) / total_possible) * 100
                score_label = "Final Score (%) - Calculated"

            # Display statistics
            st.subheader("Statistics")

            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric(f"Average {score_label}", f"{final_scores.mean():.2f}%")
            with col2:
                st.metric(f"Highest {score_label}", f"{final_scores.max():.2f}%")
            with col3:
                st.metric(f"Lowest {score_label}", f"{final_scores.min():.2f}%")
            
            # Question difficulty
            st.subheader("Question Difficulty")
            question_avg = {q: results[q].mean() for q in score_cols}
            question_df = pd.DataFrame({
                'Question': list(question_avg.keys()),
                'Average Score': list(question_avg.values())
            }).sort_values('Average Score')
            
            fig3, ax3 = plt.subplots(figsize=(10, 6))
            sns.barplot(x='Question', y='Average Score', data=question_df, ax=ax3)
            ax3.set_title('Average Score by Question (Lower = More Difficult)')
            plt.tight_layout()
            
            st.pyplot(fig3)
            st.markdown(get_image_download_link(fig3, "question_difficulty.png", "Download Question Difficulty Plot"), unsafe_allow_html=True)
    else:
        st.warning("No results found. Please mark exams first!")

# Footer
st.sidebar.markdown("---")


# Suppress warnings
import warnings
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=FutureWarning)

# Import SentenceTransformer with error handling
try:
    from sentence_transformers import SentenceTransformer
except ImportError:
    st.error("SentenceTransformer library not found. Please install it with: pip install sentence-transformers")
    st.stop()

# Load or train model
def load_or_train_model():
    """Load existing model or train a new one"""
    try:
        # First check if we have a fine-tuned model
        if os.path.exists(FINE_TUNED_MODEL_PATH) and os.listdir(FINE_TUNED_MODEL_PATH):
            st.info("Loading fine-tuned model...")
            try:
                model = load_model(FINE_TUNED_MODEL_PATH)
                st.success("Model loaded successfully!")
                return model
            except Exception as e:
                st.error(f"Error loading fine-tuned model: {e}")
                st.info("Falling back to default model...")
        else:
            st.info("No fine-tuned model found.")
            
        # Check if training data exists for fine-tuning
        if os.path.exists(TRAINING_DATA):
            st.info("Training data found. Fine-tuning model...")
            try:
                model = fine_tune_model(TRAINING_DATA)
                st.success("Model trained and saved successfully!")
                return model
            except Exception as e:
                st.error(f"Error fine-tuning model: {e}")
                st.info("Falling back to default model...")
        else:
            st.warning("No training data found. Using default model.")
        
        # If we get here, use the default model
        st.info("Loading default model (all-MiniLM-L6-v2)...")
        with st.spinner("Loading default model..."):
            # Use the safely imported SentenceTransformer
            model = SentenceTransformer("all-MiniLM-L6-v2")
            st.success("Default model loaded successfully!")
            return model
            
    except Exception as e:
        st.error(f"Error loading/training model: {e}")
        st.info("Using default model as last resort.")
        # Use the safely imported SentenceTransformer
        return SentenceTransformer("all-MiniLM-L6-v2")

def check_sentence_transformer_installation():
    """Check if SentenceTransformer is properly installed and configured - console output only"""
    try:
        # Try importing the library using our safe import
        import sentence_transformers
        print(f"✅ SentenceTransformer installed (version: {sentence_transformers.__version__})")

        # Try loading a simple model to verify functionality
        try:
            print("[INFO] Verifying SentenceTransformer functionality...")
            # Use the safely imported SentenceTransformer
            model = SentenceTransformer("all-MiniLM-L6-v2")
            # Test encoding a simple sentence
            test_embedding = model.encode("This is a test sentence.")
            if len(test_embedding) > 0:
                print("✅ SentenceTransformer is working properly")
                return True
        except Exception as e:
            print(f"❌ SentenceTransformer error: {str(e)}")
            print("Try reinstalling with: pip install -U sentence-transformers")
            return False

    except ImportError:
        print("❌ SentenceTransformer not installed")
        print("Install with: pip install sentence-transformers")
        return False
    except Exception as e:
        print(f"❌ Error checking SentenceTransformer: {str(e)}")
        return False

# Run the installation check
check_sentence_transformer_installation()

# Load or train model
model = load_or_train_model()
st.session_state.model = model











