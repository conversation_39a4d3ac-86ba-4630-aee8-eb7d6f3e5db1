# Import streamlit first before any other imports that might use it
import streamlit as st
import pandas as pd
import os
import tempfile
import shutil
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns
import base64
import warnings

# Disable specific warnings
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=FutureWarning)

# Disable Streamlit's file watcher for problematic modules
os.environ["STREAMLIT_WATCH_MODULES"] = "false"
os.environ["STREAMLIT_DISABLE_WATCHER"] = "true"

# Set page configuration with professional styling
st.set_page_config(
    page_title="DIT Exam AI Marking System",
    page_icon="🎓",
    layout="wide",
    initial_sidebar_state="expanded",
    menu_items={
        'Get Help': 'https://github.com/your-repo/exam-marking-system',
        'Report a bug': "https://github.com/your-repo/exam-marking-system/issues",
        'About': "# DIT AI Exam Marking System\nAutomated exam marking using AI and machine learning."
    }
)

# Custom CSS for professional styling
st.markdown("""
<style>
    /* Hide Streamlit branding */
    #MainMenu {visibility: hidden;}
    footer {visibility: hidden;}
    header {visibility: hidden;}

    /* Main theme colors */
    :root {
        --primary-color: #1f77b4;
        --secondary-color: #ff7f0e;
        --success-color: #2ca02c;
        --warning-color: #ff9800;
        --error-color: #d62728;
        --background-color: #f8f9fa;
        --text-color: #2c3e50;
    }

    /* Custom header styling */
    .main-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 2rem 1rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        text-align: center;
        color: white;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .main-header h1 {
        margin: 0;
        font-size: 2.5rem;
        font-weight: 700;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    .main-header p {
        margin: 0.5rem 0 0 0;
        font-size: 1.2rem;
        opacity: 0.9;
    }

    /* Sidebar styling */
    .css-1d391kg {
        background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
    }

    /* Button styling */
    .stButton > button {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        width: 100%;
    }

    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
    }

    /* Progress bar styling */
    .stProgress > div > div > div > div {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    }

    /* File uploader styling */
    .stFileUploader > div {
        border: 2px dashed #667eea;
        border-radius: 15px;
        padding: 2rem;
        text-align: center;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        transition: all 0.3s ease;
    }

    .stFileUploader > div:hover {
        border-color: #764ba2;
        background: linear-gradient(135deg, #e9ecef 0%, #f8f9fa 100%);
    }

    /* Success/Error message styling */
    .stSuccess {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        border: 1px solid #c3e6cb;
        border-radius: 10px;
        padding: 1rem;
        box-shadow: 0 2px 10px rgba(40, 167, 69, 0.1);
    }

    .stError {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        border: 1px solid #f5c6cb;
        border-radius: 10px;
        padding: 1rem;
        box-shadow: 0 2px 10px rgba(220, 53, 69, 0.1);
    }

    .stWarning {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        border: 1px solid #ffeaa7;
        border-radius: 10px;
        padding: 1rem;
        box-shadow: 0 2px 10px rgba(255, 193, 7, 0.1);
    }

    /* Navigation styling */
    .css-1544g2n {
        padding: 1rem;
    }

    /* Metric cards */
    .metric-card {
        background: white;
        padding: 1.5rem;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        text-align: center;
        margin: 0.5rem;
        border-left: 4px solid #667eea;
        transition: all 0.3s ease;
    }

    .metric-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    }

    /* Table styling */
    .dataframe {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        border: none;
    }

    /* Chart container */
    .chart-container {
        background: white;
        padding: 2rem;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        margin: 1rem 0;
    }

    /* Status indicators */
    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }

    .status-success { background-color: #28a745; }
    .status-warning { background-color: #ffc107; }
    .status-error { background-color: #dc3545; }
    .status-info { background-color: #17a2b8; }

    /* Expander styling */
    .streamlit-expanderHeader {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 10px;
        border: 1px solid #dee2e6;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state variables if they don't exist
if 'questions' not in st.session_state:
    st.session_state.questions = {}
if 'marking_scheme' not in st.session_state:
    st.session_state.marking_scheme = {}
if 'model' not in st.session_state:
    st.session_state.model = None
if 'results' not in st.session_state:
    st.session_state.results = None

# Import SentenceTransformer
try:
    from sentence_transformers import SentenceTransformer
except ImportError:
    st.error("Required libraries not found. Please install sentence-transformers.")
    st.stop()

# Define paths
CURRENT_DIR = Path(__file__).parent.absolute()
BOOKLET_FOLDER = CURRENT_DIR / "exam_booklets"
RESULTS_FILE = CURRENT_DIR / "exam_results.csv"
TRAINING_DATA = CURRENT_DIR / "training_data.csv"
FINE_TUNED_MODEL_PATH = CURRENT_DIR / "fine_tuned_model"
QUESTIONS_FILE = CURRENT_DIR / "questions.csv"
MARKING_SCHEME_FILE = CURRENT_DIR / "marking_scheme.csv"

# Create directories if they don't exist
BOOKLET_FOLDER.mkdir(parents=True, exist_ok=True)
FINE_TUNED_MODEL_PATH.mkdir(parents=True, exist_ok=True)

# Now import our modules after st is defined
from examAImarking import (
    load_config, fine_tune_model, load_model,
    extract_text, extract_answers_from_text,
    evaluate_written_answer, evaluate_mcq,
    cleanup_old_models
)

# Load configuration
CONFIG = load_config()

# Helper functions
def save_uploaded_file(uploaded_file, destination_folder):
    """Save an uploaded file to the specified folder"""
    # Create destination folder if it doesn't exist
    if isinstance(destination_folder, str):
        destination_folder = Path(destination_folder)
    destination_folder.mkdir(parents=True, exist_ok=True)
    
    # Create a temporary file
    with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
        # Write the uploaded file to the temporary file
        shutil.copyfileobj(uploaded_file, tmp_file)
        
    # Move the temporary file to the destination folder
    dest_path = destination_folder / uploaded_file.name
    shutil.move(tmp_file.name, dest_path)
    
    return str(dest_path)

def get_csv_download_link(df, filename, link_text):
    """Generate a link to download a dataframe as a CSV file"""
    csv = df.to_csv(index=False)
    b64 = base64.b64encode(csv.encode()).decode()
    href = f'<a href="data:file/csv;base64,{b64}" download="{filename}">{link_text}</a>'
    return href

def get_image_download_link(fig, filename, link_text):
    """Generate a link to download a matplotlib figure as a PNG file"""
    # Use BytesIO instead of a temporary file to avoid permission issues
    import io
    buf = io.BytesIO()
    fig.savefig(buf, format='png', dpi=300, bbox_inches='tight')
    buf.seek(0)
    img_data = buf.getvalue()
    b64 = base64.b64encode(img_data).decode()
    href = f'<a href="data:image/png;base64,{b64}" download="{filename}">{link_text}</a>'
    return href

def plot_results(df):
    """Create plots for the results"""
    # Get score columns (those starting with Q)
    score_cols = [col for col in df.columns if col.startswith('Q')]

    # Plot final scores by student (use Final_Score_Percentage if available, otherwise calculate)
    fig1, ax1 = plt.subplots(figsize=(12, 6))
    if 'Final_Score_Percentage' in df.columns:
        sns.barplot(x='Student', y='Final_Score_Percentage', data=df, ax=ax1)
        ax1.set_title('Final Scores by Student (%)')
        ax1.set_ylabel('Final Score (%)')
        ax1.set_ylim(0, 100)
        # Add score labels on bars
        for i, v in enumerate(df['Final_Score_Percentage']):
            ax1.text(i, v + 1, f'{v:.1f}%', ha='center', va='bottom')
    else:
        # Fallback to calculated average if Final_Score_Percentage not available
        num_questions = len(score_cols)
        total_possible = num_questions * 100
        df['Final_Score_Calculated'] = (df[score_cols].sum(axis=1) / total_possible) * 100
        sns.barplot(x='Student', y='Final_Score_Calculated', data=df, ax=ax1)
        ax1.set_title('Final Scores by Student (%) - Calculated')
        ax1.set_ylabel('Final Score (%)')
        ax1.set_ylim(0, 100)
        # Add score labels on bars
        for i, v in enumerate(df['Final_Score_Calculated']):
            ax1.text(i, v + 1, f'{v:.1f}%', ha='center', va='bottom')

    ax1.set_xticklabels(ax1.get_xticklabels(), rotation=45)
    plt.tight_layout()

    # Plot scores by question
    fig2, ax2 = plt.subplots(figsize=(12, 8))
    df_melted = df.melt(id_vars=['Student'], value_vars=score_cols, var_name='Question', value_name='Score')
    sns.boxplot(x='Question', y='Score', data=df_melted, ax=ax2)
    ax2.set_title('Score Distribution by Question')
    plt.tight_layout()

    return fig1, fig2

def process_student_answers(model):
    """Process student answers and return results"""
    results = []
    
    # Create a container for the processing log
    log_container = st.expander("Processing Log (Click to expand/collapse)", expanded=False)
    
    # Get list of valid booklet files
    booklet_files = [f for f in os.listdir(BOOKLET_FOLDER) 
                    if f.lower().endswith((".jpg", ".png", ".jpeg", ".tif", ".pdf"))]
    
    # Check if there are any files to process
    if not booklet_files:
        st.warning("No valid student answer booklets found in the booklet folder.")
        return None
    
    # Create a progress bar
    total_files = len(booklet_files)
    progress_bar = st.progress(0)
    status_text = st.empty()
    
    # Create a container for the summary
    summary_container = st.container()
    with summary_container:
        st.subheader("Processing Summary")
        summary_table = st.empty()
        summary_data = {"Student": [], "Status": [], "Average Score": []}
    
    # Log the start of processing
    with log_container:
        st.info(f"Starting to process {total_files} student booklets...")
    
    file_count = 0
    for file in booklet_files:
        file_count += 1
        file_path = os.path.join(BOOKLET_FOLDER, file)
        
        # Update progress
        status_text.text(f"Processing {file} ({file_count}/{total_files})...")
        progress_bar.progress(file_count / total_files)
        
        # Log the current file being processed
        with log_container:
            st.markdown(f"### Processing {file}")
        
        try:
            # Check if file exists and is readable
            if not os.path.exists(file_path):
                with log_container:
                    st.error(f"File not found: {file_path}")
                continue
                
            # Extract text and answers
            with log_container:
                st.text(f"Extracting text from {file}...")
            
            text = extract_text(os.path.join(BOOKLET_FOLDER, file))
            
            if not text or text.strip() == "":
                with log_container:
                    st.error(f"No text extracted from {file}")
                
                # Update summary
                summary_data["Student"].append(file)
                summary_data["Status"].append("❌ Failed (No text extracted)")
                summary_data["Average Score"].append("N/A")
                summary_table.dataframe(pd.DataFrame(summary_data))
                continue
            
            # Use a details HTML tag instead of an expander
            with log_container:
                st.markdown(f"""
                <details>
                <summary>Extracted text from {file}</summary>
                <pre>{text}</pre>
                </details>
                """, unsafe_allow_html=True)
            
            answers = extract_answers_from_text(text)
            
            if not answers:
                with log_container:
                    st.error(f"No answers extracted from {file}")
                
                # Update summary
                summary_data["Student"].append(file)
                summary_data["Status"].append("❌ Failed (No answers extracted)")
                summary_data["Average Score"].append("N/A")
                summary_table.dataframe(pd.DataFrame(summary_data))
                continue
            
            # Use a details HTML tag instead of an expander
            with log_container:
                answer_text = "\n".join([f"{q_id}: {ans}" for q_id, ans in answers.items()])
                st.markdown(f"""
                <details>
                <summary>Extracted answers from {file}</summary>
                <pre>{answer_text}</pre>
                </details>
                """, unsafe_allow_html=True)
            
            # Process scores
            scores = {}
            for q_id, ans in answers.items():
                if q_id in st.session_state.questions:
                    if st.session_state.questions[q_id]['type'] == 'written':
                        if q_id in st.session_state.marking_scheme:
                            model_ans = str(st.session_state.marking_scheme[q_id])
                            scores[q_id] = evaluate_written_answer(ans, model_ans, model)
                            
                            # Log in container
                            with log_container:
                                st.text(f"Written question {q_id} score: {scores[q_id]}")
                        else:
                            scores[q_id] = 0.0
                            with log_container:
                                st.warning(f"No marking scheme found for {q_id}")
                    else:  # MCQ
                        if q_id in st.session_state.marking_scheme:
                            correct_ans = str(st.session_state.marking_scheme[q_id])
                            scores[q_id] = evaluate_mcq(ans, correct_ans)
                            
                            # Log in container
                            with log_container:
                                st.text(f"MCQ {q_id} - Student: '{ans}', Correct: '{correct_ans}', Score: {scores[q_id]}")
                        else:
                            scores[q_id] = 0.0
                            with log_container:
                                st.warning(f"No marking scheme found for {q_id}")
                else:
                    scores[q_id] = 0.0
                    with log_container:
                        st.warning(f"Unknown question ID: {q_id}")
            
            # Calculate final score as percentage dynamically
            # Each question is worth 100 points, so total possible = number of questions * 100
            num_questions = len(scores)
            total_possible_points = num_questions * 100
            total_score = sum(scores.values())
            final_score_percentage = (total_score / total_possible_points) * 100 if total_possible_points > 0 else 0

            # Calculate average score for display
            score_values = [v for v in scores.values() if isinstance(v, (int, float))]
            avg_score = sum(score_values) / len(score_values) if score_values else 0

            # Create result row with Final_Score_Percentage right after the question scores
            result_row = {"Student": file}
            result_row.update(scores)  # Add all question scores
            result_row["Final_Score_Percentage"] = round(final_score_percentage, 2)

            # Add to results
            results.append(result_row)
            
            # Update summary
            summary_data["Student"].append(file)
            summary_data["Status"].append("✅ Success")
            summary_data["Average Score"].append(f"{avg_score:.2f}")
            
            # Update summary table
            summary_table.dataframe(pd.DataFrame(summary_data))
            
            # Log success
            with log_container:
                st.success(f"Successfully processed {file}")
                
        except Exception as e:
            # Log error
            with log_container:
                st.error(f"Failed to process {file}: {str(e)}")
                import traceback
                st.markdown(f"""
                <details>
                <summary>Error details</summary>
                <pre>{traceback.format_exc()}</pre>
                </details>
                """, unsafe_allow_html=True)
            
            # Update summary
            summary_data["Student"].append(file)
            summary_data["Status"].append(f"❌ Failed ({str(e)[:50]}...)")
            summary_data["Average Score"].append("N/A")
            
            # Update summary table
            summary_table.dataframe(pd.DataFrame(summary_data))
    
    # Clear progress indicators
    progress_bar.empty()
    status_text.empty()
    
    # Final summary
    if results:
        with log_container:
            st.success(f"Successfully processed {len(results)} out of {total_files} booklets.")
        return pd.DataFrame(results)
    else:
        with log_container:
            st.error("No booklets were successfully processed.")
        return None

# Load or train model
def load_or_train_model():
    """Load existing model or train a new one"""
    try:
        # First check if we have a fine-tuned model
        if os.path.exists(FINE_TUNED_MODEL_PATH) and os.listdir(FINE_TUNED_MODEL_PATH):
            st.info("Loading fine-tuned model...")
            try:
                model = load_model(FINE_TUNED_MODEL_PATH)
                st.success("Model loaded successfully!")
                return model
            except Exception as e:
                st.error(f"Error loading fine-tuned model: {e}")
                st.info("Falling back to default model...")
        else:
            st.info("No fine-tuned model found.")
            
        # Check if training data exists for fine-tuning
        if os.path.exists(TRAINING_DATA):
            st.info("Training data found. Fine-tuning model...")
            try:
                model = fine_tune_model(TRAINING_DATA)
                st.success("Model trained and saved successfully!")
                return model
            except Exception as e:
                st.error(f"Error fine-tuning model: {e}")
                st.info("Falling back to default model...")
        else:
            st.warning("No training data found. Using default model.")
        
        # If we get here, use the default model
        st.info("Loading default model (all-MiniLM-L6-v2)...")
        with st.spinner("Loading default model..."):
            model = SentenceTransformer("all-MiniLM-L6-v2")
            st.success("Default model loaded successfully!")
            return model
            
    except Exception as e:
        st.error(f"Error loading/training model: {e}")
        st.info("Using default model as last resort.")
        return SentenceTransformer("all-MiniLM-L6-v2")

# Professional Header
st.markdown("""
<div class="main-header">
    <h1>🎓 DIT AI Exam Marking System</h1>
    <p>Intelligent Automated Assessment Platform</p>
</div>
""", unsafe_allow_html=True)

# Enhanced Sidebar
st.sidebar.markdown("""
<div style="text-align: center; padding: 1rem;">
    <h2 style="color: #667eea; margin-bottom: 0.5rem;">📚 Navigation</h2>
    <p style="color: #6c757d; font-size: 0.9rem;">Select a section to get started</p>
</div>
""", unsafe_allow_html=True)

# Check environment
def check_environment():
    """Check for common issues that might cause warnings - console output only"""
    # Check Python version
    import platform
    python_version = platform.python_version()
    print(f"[INFO] Python version: {python_version}")

    # Check Streamlit version
    streamlit_version = st.__version__
    if streamlit_version < "1.10.0":
        print(f"[WARNING] Streamlit version {streamlit_version} is older than recommended (1.10.0+)")

    # Check for required libraries
    try:
        import sentence_transformers
        print(f"✅ SentenceTransformer installed (version: {sentence_transformers.__version__})")

        # Verify SentenceTransformer functionality
        try:
            print("[INFO] Verifying SentenceTransformer...")
            model = SentenceTransformer("all-MiniLM-L6-v2")
            test_embedding = model.encode("This is a test sentence.")
            if len(test_embedding) > 0:
                print("✅ SentenceTransformer is working properly")
        except Exception as e:
            print(f"❌ SentenceTransformer error: {str(e)[:50]}...")
            print("Try reinstalling with: pip install -U sentence-transformers")
    except ImportError:
        print("❌ SentenceTransformer not installed")
        print("Install with: pip install sentence-transformers")

    try:
        import torch
        print(f"✅ PyTorch installed (version: {torch.__version__})")
        if torch.cuda.is_available():
            print(f"✅ CUDA available (version: {torch.version.cuda})")
        else:
            print("ℹ️ CUDA not available, using CPU")
    except ImportError:
        print("❌ PyTorch not installed")
        print("Install with: pip install torch")

    # Check for required directories
    from pathlib import Path
    CURRENT_DIR = Path(__file__).parent.absolute()
    BOOKLET_FOLDER = CURRENT_DIR / "exam_booklets"
    FINE_TUNED_MODEL_PATH = CURRENT_DIR / "fine_tuned_model"

    if not BOOKLET_FOLDER.exists():
        BOOKLET_FOLDER.mkdir(parents=True, exist_ok=True)
        print(f"[INFO] Created booklet folder: {BOOKLET_FOLDER}")

    if not FINE_TUNED_MODEL_PATH.exists():
        FINE_TUNED_MODEL_PATH.mkdir(parents=True, exist_ok=True)
        print(f"[INFO] Created model folder: {FINE_TUNED_MODEL_PATH}")

    # Suppress specific warnings
    import warnings
    warnings.filterwarnings("ignore", category=UserWarning)
    warnings.filterwarnings("ignore", category=FutureWarning)
    warnings.filterwarnings("ignore", message=".*sentence-transformers model found with name.*")

# Run environment check
check_environment()

# Load model
model = load_or_train_model()
st.session_state.model = model

# Enhanced navigation with icons
page_options = {
    "📁 Upload Files": "Upload Files",
    "🧠 Train Model": "Train Model",
    "✅ Mark Exams": "Mark Exams",
    "📊 View Results": "View Results"
}

selected_page = st.sidebar.radio("", list(page_options.keys()))
page = page_options[selected_page]

# Add system status in sidebar
st.sidebar.markdown("---")
st.sidebar.markdown("### 🔧 System Status")

# Model status
if st.session_state.model is not None:
    st.sidebar.markdown('<span class="status-indicator status-success"></span>**Model:** Ready', unsafe_allow_html=True)
else:
    st.sidebar.markdown('<span class="status-indicator status-error"></span>**Model:** Not loaded', unsafe_allow_html=True)

# Questions status
if st.session_state.questions:
    st.sidebar.markdown(f'<span class="status-indicator status-success"></span>**Questions:** {len(st.session_state.questions)} loaded', unsafe_allow_html=True)
else:
    st.sidebar.markdown('<span class="status-indicator status-warning"></span>**Questions:** Not loaded', unsafe_allow_html=True)

# Marking scheme status
if st.session_state.marking_scheme:
    st.sidebar.markdown(f'<span class="status-indicator status-success"></span>**Marking Scheme:** {len(st.session_state.marking_scheme)} items', unsafe_allow_html=True)
else:
    st.sidebar.markdown('<span class="status-indicator status-warning"></span>**Marking Scheme:** Not loaded', unsafe_allow_html=True)

# Booklet files status
if os.path.exists(BOOKLET_FOLDER):
    booklet_count = len([f for f in os.listdir(BOOKLET_FOLDER) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.pdf', '.tif'))])
    if booklet_count > 0:
        st.sidebar.markdown(f'<span class="status-indicator status-success"></span>**Booklets:** {booklet_count} files', unsafe_allow_html=True)
    else:
        st.sidebar.markdown('<span class="status-indicator status-warning"></span>**Booklets:** No files', unsafe_allow_html=True)
else:
    st.sidebar.markdown('<span class="status-indicator status-error"></span>**Booklets:** Folder missing', unsafe_allow_html=True)

# Upload Files Page
if page == "Upload Files":
    st.markdown("## 📁 Upload Files")
    st.markdown("Upload your exam questions, marking scheme, training data, and student answer booklets.")

    # Create tabs for better organization
    tab1, tab2, tab3, tab4 = st.tabs(["📝 Questions", "📋 Marking Scheme", "📚 Training Data", "📄 Student Booklets"])

    with tab1:
        st.markdown("### 📝 Upload Questions")
        st.markdown("Upload a CSV file containing your exam questions with columns: `question_id`, `question_type`, `question_text`")

        # Create columns for better layout
        col1, col2 = st.columns([2, 1])

        with col1:
            questions_file = st.file_uploader(
                "Choose questions CSV file",
                type=["csv"],
                key="questions_uploader",
                help="CSV file with columns: question_id, question_type, question_text"
            )
    
        with col2:
            if st.session_state.questions:
                st.markdown("#### ✅ Status")
                st.success(f"{len(st.session_state.questions)} questions loaded")
            else:
                st.markdown("#### ⏳ Status")
                st.info("No questions loaded yet")

        if questions_file:
            try:
                # Save the uploaded file
                questions_path = save_uploaded_file(questions_file, ".")

                # Load questions
                df_questions = pd.read_csv(questions_path)

                # Display preview
                st.markdown("#### 📋 Preview")
                st.dataframe(df_questions, use_container_width=True)

                # Store questions in session state
                questions = {}
                for _, row in df_questions.iterrows():
                    q_id = row['question_id']
                    q_type = row['question_type']
                    q_text = row['question_text']
                    questions[q_id] = {'type': q_type, 'text': q_text}

                st.session_state.questions = questions
                st.success(f"✅ Successfully loaded {len(questions)} questions!")

            except Exception as e:
                st.error(f"❌ Error loading questions: {str(e)}")

    with tab2:
        st.markdown("### 📋 Upload Marking Scheme")
        st.markdown("Upload a CSV file containing the correct answers with columns: `question_id`, `model_answer` or `correct_option`")

        # Create columns for better layout
        col1, col2 = st.columns([2, 1])

        with col1:
            marking_scheme_file = st.file_uploader(
                "Choose marking scheme CSV file",
                type=["csv"],
                key="marking_scheme_uploader",
                help="CSV file with columns: question_id, model_answer (for written) or correct_option (for MCQ)"
            )
    
        with col2:
            if st.session_state.marking_scheme:
                st.markdown("#### ✅ Status")
                st.success(f"{len(st.session_state.marking_scheme)} answers loaded")
            else:
                st.markdown("#### ⏳ Status")
                st.info("No marking scheme loaded yet")

        if marking_scheme_file:
            try:
                # Save the uploaded file
                marking_scheme_path = save_uploaded_file(marking_scheme_file, ".")

                # Load marking scheme
                df_marking = pd.read_csv(marking_scheme_path)

                # Display preview
                st.markdown("#### 📋 Preview")
                st.dataframe(df_marking, use_container_width=True)

                # Store marking scheme in session state
                marking_scheme = {}
                for _, row in df_marking.iterrows():
                    q_id = row['question_id']
                    if 'model_answer' in row and not pd.isna(row['model_answer']):
                        marking_scheme[q_id] = str(row['model_answer'])
                    elif 'correct_option' in row and not pd.isna(row['correct_option']):
                        marking_scheme[q_id] = str(row['correct_option'])

                st.session_state.marking_scheme = marking_scheme
                st.success(f"✅ Successfully loaded marking scheme!")

            except Exception as e:
                st.error(f"❌ Error loading marking scheme: {str(e)}")

    with tab3:
        st.markdown("### 📚 Upload Training Data (Optional)")
        st.markdown("Upload training data to improve the AI model's performance on your specific exam format.")

        # Create columns for better layout
        col1, col2 = st.columns([2, 1])

        with col1:
            training_data_file = st.file_uploader(
                "Choose training data CSV file",
                type=["csv"],
                key="training_data_uploader",
                help="Optional: CSV file with student answers and scores for model training"
            )
    
        with col2:
            if os.path.exists(TRAINING_DATA):
                df_check = pd.read_csv(TRAINING_DATA, on_bad_lines='skip')
                st.markdown("#### ✅ Status")
                st.success(f"{len(df_check)} training examples")
            else:
                st.markdown("#### ⏳ Status")
                st.info("No training data uploaded")

        if training_data_file:
            try:
                # Save the uploaded file
                training_data_path = save_uploaded_file(training_data_file, str(TRAINING_DATA.parent))

                # Rename to match expected filename
                os.rename(training_data_path, TRAINING_DATA)

                # Load and display training data
                df_training = pd.read_csv(TRAINING_DATA, on_bad_lines='skip')

                # Display preview
                st.markdown("#### 📋 Preview")
                st.dataframe(df_training, use_container_width=True)

                st.success(f"✅ Successfully loaded training data with {len(df_training)} examples!")

            except Exception as e:
                st.error(f"❌ Error loading training data: {str(e)}")

    with tab4:
        st.markdown("### 📄 Upload Student Answer Booklets")
        st.markdown("Upload scanned images or PDFs of student answer sheets for marking.")

        # Create columns for better layout
        col1, col2 = st.columns([2, 1])

        with col1:
            student_files = st.file_uploader(
                "Choose student answer files",
                type=["jpg", "jpeg", "png", "pdf", "tif"],
                accept_multiple_files=True,
                help="Upload scanned images or PDFs of student answer sheets"
            )
    
        with col2:
            if os.path.exists(BOOKLET_FOLDER):
                booklet_count = len([f for f in os.listdir(BOOKLET_FOLDER) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.pdf', '.tif'))])
                st.markdown("#### 📊 Status")
                if booklet_count > 0:
                    st.success(f"{booklet_count} booklets uploaded")
                else:
                    st.info("No booklets uploaded yet")
            else:
                st.markdown("#### ⏳ Status")
                st.info("Booklet folder not found")

        if student_files:
            # Clear existing files option
            if st.checkbox("🗑️ Clear existing booklets before uploading new ones"):
                for file in os.listdir(BOOKLET_FOLDER):
                    os.remove(BOOKLET_FOLDER / file)
                st.info("🧹 Cleared existing booklets.")

            # Save uploaded files
            for uploaded_file in student_files:
                save_uploaded_file(uploaded_file, BOOKLET_FOLDER)

            st.success(f"✅ Successfully uploaded {len(student_files)} student answer booklets!")

            # Display uploaded files in a nice grid
            st.markdown("#### 📁 Uploaded Booklets")
            booklet_files = [f for f in os.listdir(BOOKLET_FOLDER) if f.lower().endswith((".jpg", ".png", ".jpeg"))]

            if booklet_files:
                cols = st.columns(4)
                for i, file in enumerate(booklet_files[:8]):  # Show max 8 images
                    with cols[i % 4]:
                        st.image(str(BOOKLET_FOLDER / file), caption=file, use_column_width=True)

                if len(booklet_files) > 8:
                    st.info(f"Showing 8 of {len(booklet_files)} uploaded files...")
            else:
                st.info("No image files to preview (PDFs and other formats uploaded successfully)")

# Train Model Page
if page == "Train Model":
    st.title("Train Model")
    
    # Upload Training Data
    st.header("Upload Training Data")
    training_data_file = st.file_uploader("Upload training data CSV file", type=["csv"], key="training_data_uploader")
    
    if training_data_file:
        try:
            # Save the uploaded file
            with open(TRAINING_DATA, "wb") as f:
                f.write(training_data_file.getbuffer())
            
            # Load and display training data
            df_training = pd.read_csv(TRAINING_DATA, on_bad_lines='skip')
            st.dataframe(df_training, use_container_width=True)
            
            st.success(f"Successfully loaded training data with {len(df_training)} examples!")
            
        except Exception as e:
            st.error(f"Error loading training data: {str(e)}")
    
    # Create Sample Training Data
    #st.header("Create Sample Training Data")
    #num_examples = st.slider("Number of examples", min_value=10, max_value=100, value=20, step=10)
    
    #if st.button("Generate Sample Training Data"):
      #  with st.spinner("Generating sample training data..."):
       #     try:
         #       from create_samples import create_sample_training_data
          #      create_sample_training_data(TRAINING_DATA, num_examples)
                
                # Load and display the generated data
             #   df_training = pd.read_csv(TRAINING_DATA)
              # st.dataframe(df_training)
                
               # st.success(f"Successfully generated {len(df_training)} training examples!")
                
            #except Exception as e:
            #    st.error(f"Error generating training data: {str(e)}")
    
    # Train Model Button
    st.header("Train Model")
    
    # Advanced training options
    with st.expander("Advanced Training Options", expanded=False):
        epochs = st.slider("Number of epochs", min_value=1, max_value=10, value=int(CONFIG['training']['epochs']))
        batch_size = st.slider("Batch size", min_value=8, max_value=64, value=int(CONFIG['training']['batch_size']))
        warmup_steps = st.slider("Warmup steps", min_value=0, max_value=500, value=int(CONFIG['training']['warmup_steps']))
        
        # Update config
        CONFIG['training']['epochs'] = str(epochs)
        CONFIG['training']['batch_size'] = str(batch_size)
        CONFIG['training']['warmup_steps'] = str(warmup_steps)
    
    if st.button("Train Model"):
        if not os.path.exists(TRAINING_DATA):
            st.error("No training data found. Please upload or generate training data first.")
        else:
            with st.spinner("Training model..."):
                try:
                    # Clean up old models before training
                    cleanup_old_models(keep_latest=3)
                    
                    # Train the model
                    model = fine_tune_model(TRAINING_DATA)
                    st.session_state.model = model
                    st.success("Model trained successfully!")
                    
                    # Show model info
                    st.info("Model information:")
                    st.json({
                        "model_type": "SentenceTransformer",
                        "base_model": "all-MiniLM-L6-v2",
                        "embedding_dimension": 384,
                        "training_examples": len(pd.read_csv(TRAINING_DATA)),
                        "epochs": epochs,
                        "batch_size": batch_size
                    })
                    
                except Exception as e:
                    st.error(f"Error training model: {str(e)}")
                    import traceback
                    st.code(traceback.format_exc())

# Mark Exams Page
elif page == "Mark Exams":
    st.markdown("## ✅ Mark Exams")
    st.markdown("Automatically mark student answer booklets using AI.")

    # Pre-flight checks with better styling
    checks_passed = True

    # Create status cards
    col1, col2, col3 = st.columns(3)

    with col1:
        if st.session_state.questions:
            st.markdown("""
            <div class="metric-card">
                <h4>📝 Questions</h4>
                <p style="color: #28a745; font-size: 1.2rem; font-weight: bold;">✅ {count} Loaded</p>
            </div>
            """.format(count=len(st.session_state.questions)), unsafe_allow_html=True)
        else:
            st.markdown("""
            <div class="metric-card">
                <h4>📝 Questions</h4>
                <p style="color: #dc3545; font-size: 1.2rem; font-weight: bold;">❌ Not Loaded</p>
            </div>
            """, unsafe_allow_html=True)
            checks_passed = False

    with col2:
        if st.session_state.marking_scheme:
            st.markdown("""
            <div class="metric-card">
                <h4>📋 Marking Scheme</h4>
                <p style="color: #28a745; font-size: 1.2rem; font-weight: bold;">✅ {count} Items</p>
            </div>
            """.format(count=len(st.session_state.marking_scheme)), unsafe_allow_html=True)
        else:
            st.markdown("""
            <div class="metric-card">
                <h4>📋 Marking Scheme</h4>
                <p style="color: #dc3545; font-size: 1.2rem; font-weight: bold;">❌ Not Loaded</p>
            </div>
            """, unsafe_allow_html=True)
            checks_passed = False

    with col3:
        booklet_files = [f for f in os.listdir(BOOKLET_FOLDER) if f.lower().endswith((".jpg", ".png", ".jpeg", ".tif", ".pdf"))]
        if booklet_files:
            st.markdown("""
            <div class="metric-card">
                <h4>📄 Student Booklets</h4>
                <p style="color: #28a745; font-size: 1.2rem; font-weight: bold;">✅ {count} Files</p>
            </div>
            """.format(count=len(booklet_files)), unsafe_allow_html=True)
        else:
            st.markdown("""
            <div class="metric-card">
                <h4>📄 Student Booklets</h4>
                <p style="color: #dc3545; font-size: 1.2rem; font-weight: bold;">❌ No Files</p>
            </div>
            """, unsafe_allow_html=True)
            checks_passed = False

    if not checks_passed:
        st.error("⚠️ Please complete the setup in the 'Upload Files' section before marking exams.")
        st.stop()

    # Display exam overview
    with st.expander("📋 Exam Overview", expanded=False):
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("#### 📝 Questions")
            for q_id, q_info in st.session_state.questions.items():
                st.markdown(f"**{q_id}** ({q_info['type']}): {q_info['text'][:100]}...")

        with col2:
            st.markdown("#### 📋 Marking Scheme")
            for q_id, answer in st.session_state.marking_scheme.items():
                st.markdown(f"**{q_id}**: {str(answer)[:50]}...")

    # Mark exams section
    st.markdown("---")
    st.markdown("### 🚀 Start Marking Process")

    if st.session_state.model is not None:
        col1, col2 = st.columns([1, 3])

        with col1:
            mark_button = st.button("🎯 Mark All Exams", type="primary", use_container_width=True)

        with col2:
            st.info(f"Ready to mark {len(booklet_files)} student booklets using AI model.")

        if mark_button:
            with st.spinner("🔄 Initializing marking process..."):
                try:
                    # Process student answers
                    results = process_student_answers(st.session_state.model)

                    if results is not None and not results.empty:
                        # Save results
                        results.to_csv(RESULTS_FILE, index=False)
                        st.session_state.results = results

                        st.success("🎉 Exams marked successfully!")

                        # Display quick results summary
                        st.markdown("#### 📊 Quick Results Summary")
                        col1, col2, col3 = st.columns(3)

                        with col1:
                            avg_score = results['Final_Score_Percentage'].mean() if 'Final_Score_Percentage' in results.columns else 0
                            st.metric("Average Score", f"{avg_score:.1f}%")

                        with col2:
                            max_score = results['Final_Score_Percentage'].max() if 'Final_Score_Percentage' in results.columns else 0
                            st.metric("Highest Score", f"{max_score:.1f}%")

                        with col3:
                            min_score = results['Final_Score_Percentage'].min() if 'Final_Score_Percentage' in results.columns else 0
                            st.metric("Lowest Score", f"{min_score:.1f}%")

                        # Display results table
                        st.markdown("#### 📋 Detailed Results")
                        st.dataframe(results, use_container_width=True)

                        # Download link
                        st.markdown(get_csv_download_link(results, "exam_results.csv", "📥 Download Results CSV"), unsafe_allow_html=True)

                        # Navigation hint
                        st.info("💡 View detailed analytics and visualizations in the 'View Results' section.")
                    else:
                        st.warning("⚠️ No results were generated. Please check the student answer booklets.")

                except Exception as e:
                    st.error(f"❌ Error marking exams: {str(e)}")
    else:
        st.error("❌ AI model not loaded. Please check the system status or restart the application.")

# View Results Page
elif page == "View Results":
    st.title("View Results")
    
    # Check if results exist
    if os.path.exists(RESULTS_FILE):
        # Load results
        results = pd.read_csv(RESULTS_FILE)
        st.session_state.results = results
        
        # Display results
        st.subheader("Exam Results")
        st.dataframe(results)
        
        # Download link
        st.markdown(get_csv_download_link(results, "exam_results.csv", "Download Results CSV"), unsafe_allow_html=True)
        
        # Create plots
        fig1, fig2 = plot_results(results)
        
        if fig1 and fig2:
            # Display plots
            st.subheader("Average Scores by Student")
            fig1.tight_layout()  # Ensure proper layout
            st.pyplot(fig1, clear_figure=True)  # Clear figure after displaying
            st.markdown(get_image_download_link(fig1, "average_scores.png", "Download Average Scores Plot"), unsafe_allow_html=True)

            st.subheader("Score Distribution by Question")
            fig2.tight_layout()  # Ensure proper layout
            st.pyplot(fig2, clear_figure=True)  # Clear figure after displaying
            st.markdown(get_image_download_link(fig2, "question_scores.png", "Download Question Scores Plot"), unsafe_allow_html=True)

            # Calculate statistics
            score_cols = [col for col in results.columns if col.startswith('Q')]

            # Use Final_Score_Percentage if available, otherwise calculate it
            if 'Final_Score_Percentage' in results.columns:
                final_scores = results['Final_Score_Percentage']
                score_label = "Final Score (%)"
            else:
                # Calculate final scores dynamically
                num_questions = len(score_cols)
                total_possible = num_questions * 100
                final_scores = (results[score_cols].sum(axis=1) / total_possible) * 100
                score_label = "Final Score (%) - Calculated"

            # Display statistics
            st.subheader("Statistics")

            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric(f"Average {score_label}", f"{final_scores.mean():.2f}%")
            with col2:
                st.metric(f"Highest {score_label}", f"{final_scores.max():.2f}%")
            with col3:
                st.metric(f"Lowest {score_label}", f"{final_scores.min():.2f}%")
            
            # Question difficulty
            st.subheader("Question Difficulty")
            question_avg = {q: results[q].mean() for q in score_cols}
            question_df = pd.DataFrame({
                'Question': list(question_avg.keys()),
                'Average Score': list(question_avg.values())
            }).sort_values('Average Score')
            
            fig3, ax3 = plt.subplots(figsize=(10, 6))
            sns.barplot(x='Question', y='Average Score', data=question_df, ax=ax3)
            ax3.set_title('Average Score by Question (Lower = More Difficult)')
            plt.tight_layout()
            
            st.pyplot(fig3)
            st.markdown(get_image_download_link(fig3, "question_difficulty.png", "Download Question Difficulty Plot"), unsafe_allow_html=True)
    else:
        st.warning("No results found. Please mark exams first!")

# Footer
st.sidebar.markdown("---")


# Suppress warnings
import warnings
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=FutureWarning)

# Import SentenceTransformer with error handling
try:
    from sentence_transformers import SentenceTransformer
except ImportError:
    st.error("SentenceTransformer library not found. Please install it with: pip install sentence-transformers")
    st.stop()

# Load or train model
def load_or_train_model():
    """Load existing model or train a new one"""
    try:
        # First check if we have a fine-tuned model
        if os.path.exists(FINE_TUNED_MODEL_PATH) and os.listdir(FINE_TUNED_MODEL_PATH):
            st.info("Loading fine-tuned model...")
            try:
                model = load_model(FINE_TUNED_MODEL_PATH)
                st.success("Model loaded successfully!")
                return model
            except Exception as e:
                st.error(f"Error loading fine-tuned model: {e}")
                st.info("Falling back to default model...")
        else:
            st.info("No fine-tuned model found.")
            
        # Check if training data exists for fine-tuning
        if os.path.exists(TRAINING_DATA):
            st.info("Training data found. Fine-tuning model...")
            try:
                model = fine_tune_model(TRAINING_DATA)
                st.success("Model trained and saved successfully!")
                return model
            except Exception as e:
                st.error(f"Error fine-tuning model: {e}")
                st.info("Falling back to default model...")
        else:
            st.warning("No training data found. Using default model.")
        
        # If we get here, use the default model
        st.info("Loading default model (all-MiniLM-L6-v2)...")
        with st.spinner("Loading default model..."):
            # Use the safely imported SentenceTransformer
            model = SentenceTransformer("all-MiniLM-L6-v2")
            st.success("Default model loaded successfully!")
            return model
            
    except Exception as e:
        st.error(f"Error loading/training model: {e}")
        st.info("Using default model as last resort.")
        # Use the safely imported SentenceTransformer
        return SentenceTransformer("all-MiniLM-L6-v2")

def check_sentence_transformer_installation():
    """Check if SentenceTransformer is properly installed and configured - console output only"""
    try:
        # Try importing the library using our safe import
        import sentence_transformers
        print(f"✅ SentenceTransformer installed (version: {sentence_transformers.__version__})")

        # Try loading a simple model to verify functionality
        try:
            print("[INFO] Verifying SentenceTransformer functionality...")
            # Use the safely imported SentenceTransformer
            model = SentenceTransformer("all-MiniLM-L6-v2")
            # Test encoding a simple sentence
            test_embedding = model.encode("This is a test sentence.")
            if len(test_embedding) > 0:
                print("✅ SentenceTransformer is working properly")
                return True
        except Exception as e:
            print(f"❌ SentenceTransformer error: {str(e)}")
            print("Try reinstalling with: pip install -U sentence-transformers")
            return False

    except ImportError:
        print("❌ SentenceTransformer not installed")
        print("Install with: pip install sentence-transformers")
        return False
    except Exception as e:
        print(f"❌ Error checking SentenceTransformer: {str(e)}")
        return False

# Run the installation check
check_sentence_transformer_installation()

# Load or train model
model = load_or_train_model()
st.session_state.model = model











