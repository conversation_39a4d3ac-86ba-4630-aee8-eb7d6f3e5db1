#!/usr/bin/env python3
"""
Display the final exam results with dynamic scoring.
"""

import pandas as pd

def show_results():
    """Display the final exam results."""
    
    # Read the results
    df = pd.read_csv('./exam_results.csv')
    
    print('=' * 80)
    print('FINAL EXAM RESULTS WITH DYNAMIC SCORING')
    print('=' * 80)
    print()
    
    # Display the table
    print('Results Table:')
    print(df.to_string(index=False, float_format='%.2f'))
    print()
    
    # Calculate summary statistics
    q_cols = [col for col in df.columns if col.startswith('Q')]
    
    print('Summary Statistics:')
    print(f'Number of Students: {len(df)}')
    print(f'Number of Questions: {len(q_cols)}')
    print(f'Total Possible Points per Student: {len(q_cols) * 100}')
    print()
    
    print('Final Score Statistics:')
    print(f'Highest Score: {df["Final_Score_Percentage"].max():.2f}%')
    print(f'Lowest Score: {df["Final_Score_Percentage"].min():.2f}%')
    print(f'Average Score: {df["Final_Score_Percentage"].mean():.2f}%')
    print(f'Median Score: {df["Final_Score_Percentage"].median():.2f}%')
    print()
    
    print('Individual Student Performance:')
    for _, row in df.iterrows():
        student = row['Student']
        final_score = row['Final_Score_Percentage']
        q_scores = [row[f'Q{i}'] for i in range(1, 7)]
        total_raw = sum(q_scores)
        print(f'{student:25} | Final: {final_score:6.2f}% | Raw Total: {total_raw:6.1f}/600')
    
    print()
    print('=' * 80)
    print('DYNAMIC SCORING VERIFICATION')
    print('=' * 80)
    
    # Verify dynamic calculation
    for _, row in df.iterrows():
        student = row['Student']
        q_scores = [row[f'Q{i}'] for i in range(1, 7)]
        total_score = sum(q_scores)
        num_questions = 6
        total_possible = num_questions * 100
        calculated_percentage = (total_score / total_possible) * 100
        stored_percentage = row['Final_Score_Percentage']
        
        print(f'{student}:')
        print(f'  Calculation: ({total_score:.1f} / {total_possible}) × 100 = {calculated_percentage:.2f}%')
        print(f'  Stored:      {stored_percentage:.2f}%')
        print(f'  Match:       {"✓" if abs(calculated_percentage - stored_percentage) < 0.01 else "✗"}')
        print()

if __name__ == "__main__":
    show_results()
