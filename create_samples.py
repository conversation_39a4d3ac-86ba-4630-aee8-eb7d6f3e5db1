import os
import pandas as pd
import random
from pathlib import Path
import logging
from PIL import Image, ImageDraw, ImageFont
import textwrap

# Setup logging
logging.basicConfig(
    filename="sample_creation.log", 
    level=logging.INFO, 
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

def create_sample_questions(output_file="questions.csv"):
    """
    Create sample questions CSV file.
    
    Args:
        output_file (str): Path to save the questions CSV
    """
    questions = [
        {"question_id": "Q1", "question_type": "written", "question_text": "Explain what machine learning is and how it relates to artificial intelligence."},
        {"question_id": "Q2", "question_type": "written", "question_text": "Describe what a neural network is and how it works."},
        {"question_id": "Q3", "question_type": "mcq", "question_text": "Which of the following is NOT a type of machine learning? A) Supervised B) Unsupervised C) Deterministic D) Reinforcement"},
        {"question_id": "Q4", "question_type": "mcq", "question_text": "Which algorithm is commonly used for classification? A) Linear Regression B) K-means C) Decision Trees D) Principal Component Analysis"},
        {"question_id": "Q5", "question_type": "mcq", "question_text": "What does CNN stand for in deep learning? A) Complex Neural Network B) Convolutional Neural Network C) Computational Neural Network D) Cognitive Neural Network"},
        {"question_id": "Q6", "question_type": "written", "question_text": "Explain what cybersecurity is and why it's important in modern computing."}
    ]
    
    df = pd.DataFrame(questions)
    df.to_csv(output_file, index=False)
    logger.info(f"Created sample questions file: {output_file}")
    print(f"[INFO] Created sample questions file: {output_file}")
    return questions

def create_sample_marking_scheme(output_file="marking_scheme.csv", questions=None):
    """
    Create sample marking scheme CSV file.
    
    Args:
        output_file (str): Path to save the marking scheme CSV
        questions (dict): Dictionary of questions
    """
    marking_scheme = [
        {"question_id": "Q1", "model_answer": "Machine learning is a field of artificial intelligence that uses algorithms to learn from data. It enables computers to improve their performance on tasks through experience without being explicitly programmed."},
        {"question_id": "Q2", "model_answer": "A neural network is a system of artificial neurons that mimic the human brain's structure. It consists of layers of interconnected nodes that process information, learn patterns, and make decisions or predictions."},
        {"question_id": "Q3", "correct_option": "C"},
        {"question_id": "Q4", "correct_option": "C"},
        {"question_id": "Q5", "correct_option": "B"},
        {"question_id": "Q6", "model_answer": "Cybersecurity involves protecting systems, networks, and programs from digital attacks. It's important because it safeguards sensitive data, prevents unauthorized access, and ensures the integrity and availability of digital resources."}
    ]
    
    df = pd.DataFrame(marking_scheme)
    df.to_csv(output_file, index=False)
    logger.info(f"Created sample marking scheme file: {output_file}")
    print(f"[INFO] Created sample marking scheme file: {output_file}")
    return marking_scheme

def load_questions_and_marking_scheme(questions_file="questions.csv", marking_scheme_file="marking_scheme.csv"):
    """
    Load questions and marking scheme from CSV files.
    
    Args:
        questions_file (str): Path to the questions CSV file
        marking_scheme_file (str): Path to the marking scheme CSV file
        
    Returns:
        tuple: (questions_dict, marking_scheme_dict)
    """
    questions = {}
    marking_scheme = {}
    
    # Load questions
    try:
        if Path(questions_file).exists():
            df_questions = pd.read_csv(questions_file)
            for _, row in df_questions.iterrows():
                q_id = row['question_id']
                q_type = row['question_type']  # 'written' or 'mcq'
                q_text = row['question_text']
                questions[q_id] = {'type': q_type, 'text': q_text}
            logger.info(f"Loaded {len(questions)} questions from {questions_file}")
        else:
            logger.warning(f"Questions file not found: {questions_file}")
            # Create sample questions
            q_list = create_sample_questions(questions_file)
            for q in q_list:
                q_id = q['question_id']
                questions[q_id] = {'type': q['question_type'], 'text': q['question_text']}
    except Exception as e:
        logger.error(f"Error loading questions: {e}")
        # Create sample questions
        q_list = create_sample_questions(questions_file)
        for q in q_list:
            q_id = q['question_id']
            questions[q_id] = {'type': q['question_type'], 'text': q['question_text']}
    
    # Load marking scheme
    try:
        if Path(marking_scheme_file).exists():
            df_marking = pd.read_csv(marking_scheme_file)
            for _, row in df_marking.iterrows():
                q_id = row['question_id']
                if 'model_answer' in row and not pd.isna(row['model_answer']):
                    marking_scheme[q_id] = str(row['model_answer'])
                elif 'correct_option' in row and not pd.isna(row['correct_option']):
                    marking_scheme[q_id] = str(row['correct_option'])
            logger.info(f"Loaded marking scheme from {marking_scheme_file}")
        else:
            logger.warning(f"Marking scheme file not found: {marking_scheme_file}")
            # Create sample marking scheme
            ms_list = create_sample_marking_scheme(marking_scheme_file)
            for ms in ms_list:
                q_id = ms['question_id']
                if 'model_answer' in ms:
                    marking_scheme[q_id] = str(ms['model_answer'])
                elif 'correct_option' in ms:
                    marking_scheme[q_id] = str(ms['correct_option'])
    except Exception as e:
        logger.error(f"Error loading marking scheme: {e}")
        # Create sample marking scheme
        ms_list = create_sample_marking_scheme(marking_scheme_file)
        for ms in ms_list:
            q_id = ms['question_id']
            if 'model_answer' in ms:
                marking_scheme[q_id] = str(ms['model_answer'])
            elif 'correct_option' in ms:
                marking_scheme[q_id] = str(ms['correct_option'])
    
    return questions, marking_scheme

def create_sample_student_answers(output_dir="exam_booklets", num_students=4):
    """
    Create sample student answer booklets as images.
    
    Args:
        output_dir (str): Directory to save the sample booklets
        num_students (int): Number of student booklets to create
    """
    # Create directory if it doesn't exist
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    # Load questions and marking scheme
    questions, marking_scheme = load_questions_and_marking_scheme()
    
    if not questions or not marking_scheme:
        print("[ERROR] Failed to load questions or marking scheme.")
        return
    
    # Generate sample student answers
    student_answers = []
    for i in range(num_students):
        answers = {}
        for q_id, q_info in questions.items():
            if q_info['type'] == 'written':
                # Generate a written answer with some variation
                if q_id in marking_scheme:
                    model_ans = marking_scheme[q_id]
                    # Introduce some variation
                    words = model_ans.split()
                    if len(words) > 5:
                        # Remove or change some words
                        if i % 2 == 0:  # For even-numbered students
                            words = words[:len(words)-2]  # Remove last two words
                        else:  # For odd-numbered students
                            words[2] = "different"  # Change a word
                    
                    answers[q_id] = " ".join(words)
                else:
                    answers[q_id] = f"Student {i+1}'s answer for {q_id}"
            else:  # MCQ
                if q_id in marking_scheme:
                    correct = marking_scheme[q_id]
                    # Sometimes give correct answer, sometimes wrong
                    if i % 2 == 0:  # Even-numbered students get it right
                        answers[q_id] = correct
                    else:  # Odd-numbered students get it wrong
                        options = ['A', 'B', 'C', 'D']
                        options.remove(correct)
                        answers[q_id] = random.choice(options)
                else:
                    answers[q_id] = random.choice(['A', 'B', 'C', 'D'])
        
        student_answers.append(answers)
    
    # Create images for each student
    for i, answers in enumerate(student_answers[:num_students]):
        # Create a blank image
        img = Image.new('RGB', (1000, 1400), color='white')
        draw = ImageDraw.Draw(img)
        
        try:
            font = ImageFont.truetype("arial.ttf", 20)
        except IOError:
            try:
                # Try a different common font
                font = ImageFont.truetype("DejaVuSans.ttf", 20)
            except IOError:
                # Fall back to default
                font = ImageFont.load_default()
        
        # Add header
        draw.text((50, 50), f"Student {i+1} - Exam Booklet", fill='black', font=font)
        
        y_pos = 100
        for q_id, answer in answers.items():
            q_text = questions[q_id]['text']
            draw.text((50, y_pos), f"{q_id}: {q_text}", fill='black', font=font)
            y_pos += 40
            
            # Format the answer differently for MCQ vs written
            if questions[q_id]['type'] == 'mcq':
                draw.text((70, y_pos), f"Answer: {answer}", fill='black', font=font)
            else:
                # Wrap long text
                lines = textwrap.wrap(answer, width=80)
                for line in lines:
                    draw.text((70, y_pos), line, fill='black', font=font)
                    y_pos += 30
            
            y_pos += 60
        
        # Save the image
        filename = f"student_{i+1}_booklet.png"
        img.save(Path(output_dir) / filename)
        print(f"[INFO] Created {filename}")
    
    print(f"[INFO] Created {num_students} sample student booklets in {output_dir}")

def create_sample_training_data(output_file="training_data.csv", num_examples=20):
    """
    Create sample training data for fine-tuning the model.
    
    Args:
        output_file (str): Path to save the training data CSV
        num_examples (int): Number of training examples to create
    """
    # Load questions and marking scheme
    questions, marking_scheme = load_questions_and_marking_scheme()
    
    if not questions or not marking_scheme:
        print("[ERROR] Failed to load questions or marking scheme.")
        return
    
    # Generate training data
    training_data = []
    
    # Get written questions and their model answers
    written_questions = {q_id: q_info for q_id, q_info in questions.items() if q_info['type'] == 'written'}
    
    for q_id, q_info in written_questions.items():
        if q_id in marking_scheme:
            model_answer = marking_scheme[q_id]
            
            # Generate variations of the model answer with different similarity levels
            for i in range(num_examples // len(written_questions)):
                # Create a student answer with varying similarity
                similarity = random.uniform(0.3, 1.0)
                
                if similarity > 0.9:  # Very similar
                    # Just change a few words
                    words = model_answer.split()
                    if len(words) > 5:
                        idx = random.randint(0, len(words) - 1)
                        words[idx] = random.choice(["the", "a", "an", "this", "that"])
                    student_answer = " ".join(words)
                    
                elif similarity > 0.7:  # Somewhat similar
                    # Remove some words and add some
                    words = model_answer.split()
                    if len(words) > 10:
                        # Remove ~20% of words
                        to_remove = int(len(words) * 0.2)
                        for _ in range(to_remove):
                            idx = random.randint(0, len(words) - 1)
                            words.pop(idx)
                    student_answer = " ".join(words)
                    
                elif similarity > 0.5:  # Moderately similar
                    # Keep only half the content
                    words = model_answer.split()
                    student_answer = " ".join(words[:len(words)//2])
                    
                else:  # Not very similar
                    # Just a few relevant words
                    words = model_answer.split()
                    student_answer = " ".join(random.sample(words, min(5, len(words))))
                
                # Add to training data
                training_data.append({
                    "question_id": q_id,
                    "student_answer": student_answer,
                    "model_answer": model_answer,
                    "similarity": similarity
                })
    
    # Save to CSV
    df = pd.DataFrame(training_data)
    df.to_csv(output_file, index=False)
    print(f"[INFO] Created {len(df)} training examples in {output_file}")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Create sample data for exam marking")
    parser.add_argument("--questions", default="questions.csv", help="Path to save questions CSV")
    parser.add_argument("--marking-scheme", default="marking_scheme.csv", help="Path to save marking scheme CSV")
    parser.add_argument("--booklets", default="sample_booklets", help="Directory to save sample booklets")
    parser.add_argument("--training-data", default="training_data.csv", help="Path to save training data CSV")
    parser.add_argument("--num-students", type=int, default=4, help="Number of sample student booklets to create")
    
    args = parser.parse_args()
    
    print("[INFO] Creating sample data...")
    create_sample_questions(args.questions)
    create_sample_marking_scheme(args.marking_scheme)
    create_sample_student_answers(args.booklets, args.num_students)
    create_sample_training_data(args.training_data)
    print("[INFO] Sample data created successfully.")



