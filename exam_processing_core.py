#!/usr/bin/env python3
"""
Core exam processing logic - shared between CLI and Streamlit interfaces.
This module contains the business logic without any UI-specific code.
"""

import os
import pandas as pd
from pathlib import Path
from typing import Dict, List, Callable, Optional, Any
import logging

# Import the core functions from examAImarking
from examAImarking import (
    extract_text, extract_answers_from_text, 
    evaluate_written_answer, evaluate_mcq,
    load_questions_and_marking_scheme
)

logger = logging.getLogger(__name__)

class ExamProcessor:
    """Core exam processing class that handles the business logic."""
    
    def __init__(self, questions: Dict, marking_scheme: Dict, model: Any):
        """
        Initialize the exam processor.
        
        Args:
            questions (Dict): Dictionary of questions {q_id: {'type': str, 'text': str}}
            marking_scheme (Dict): Dictionary of correct answers {q_id: answer}
            model: The ML model for evaluating written answers
        """
        self.questions = questions
        self.marking_scheme = marking_scheme
        self.model = model
        
    def process_single_booklet(self, file_path: str, progress_callback: Optional[Callable] = None) -> Dict:
        """
        Process a single student booklet.
        
        Args:
            file_path (str): Path to the booklet file
            progress_callback (Callable, optional): Callback function for progress updates
                Signature: callback(stage: str, message: str, data: Any = None)
        
        Returns:
            Dict: Processing result with student scores and metadata
        """
        file_name = os.path.basename(file_path)
        
        try:
            # Stage 1: Extract text
            if progress_callback:
                progress_callback("extracting", f"Extracting text from {file_name}")
            
            text = extract_text(file_path)
            
            if not text or text.strip() == "":
                error_msg = f"No text extracted from {file_name}"
                if progress_callback:
                    progress_callback("error", error_msg)
                return {
                    "Student": file_name,
                    "status": "failed",
                    "error": "No text extracted",
                    "scores": {}
                }
            
            if progress_callback:
                progress_callback("text_extracted", f"Extracted {len(text)} characters", {"text": text})
            
            # Stage 2: Extract answers
            if progress_callback:
                progress_callback("parsing", f"Parsing answers from {file_name}")
            
            answers = extract_answers_from_text(text)
            
            if not answers:
                error_msg = f"No answers extracted from {file_name}"
                if progress_callback:
                    progress_callback("error", error_msg)
                return {
                    "Student": file_name,
                    "status": "failed", 
                    "error": "No answers extracted",
                    "scores": {}
                }
            
            if progress_callback:
                progress_callback("answers_extracted", f"Extracted {len(answers)} answers", {"answers": answers})
            
            # Stage 3: Calculate scores
            if progress_callback:
                progress_callback("scoring", f"Calculating scores for {file_name}")
            
            scores = self._calculate_scores(answers, progress_callback)
            
            # Stage 4: Calculate final score
            final_score_data = self._calculate_final_score(scores)
            
            # Create result
            result = {
                "Student": file_name,
                "status": "success",
                "scores": scores,
                **final_score_data
            }
            
            if progress_callback:
                progress_callback("completed", f"Successfully processed {file_name}", result)
            
            return result
            
        except Exception as e:
            error_msg = f"Failed to process {file_name}: {str(e)}"
            logger.error(error_msg, exc_info=True)
            if progress_callback:
                progress_callback("error", error_msg, {"exception": e})
            
            return {
                "Student": file_name,
                "status": "failed",
                "error": str(e),
                "scores": {}
            }
    
    def _calculate_scores(self, answers: Dict, progress_callback: Optional[Callable] = None) -> Dict:
        """Calculate scores for all questions."""
        scores = {}
        
        for q_id, ans in answers.items():
            if q_id in self.questions:
                if self.questions[q_id]['type'] == 'written':
                    if q_id in self.marking_scheme:
                        if self.model is not None:
                            model_ans = str(self.marking_scheme[q_id])
                            score = evaluate_written_answer(ans, model_ans, self.model)
                            scores[q_id] = score

                            if progress_callback:
                                progress_callback("question_scored", f"Written question {q_id}: {score:.2f}",
                                                {"question_id": q_id, "score": score, "type": "written"})
                        else:
                            scores[q_id] = 0.0
                            if progress_callback:
                                progress_callback("warning", f"No model available for written question {q_id}")
                    else:
                        scores[q_id] = 0.0
                        if progress_callback:
                            progress_callback("warning", f"No marking scheme found for {q_id}")
                else:  # MCQ
                    if q_id in self.marking_scheme:
                        correct_ans = str(self.marking_scheme[q_id])
                        score = evaluate_mcq(ans, correct_ans)
                        scores[q_id] = score
                        
                        if progress_callback:
                            progress_callback("question_scored", f"MCQ {q_id}: {score}", 
                                            {"question_id": q_id, "student_answer": ans, 
                                             "correct_answer": correct_ans, "score": score, "type": "mcq"})
                    else:
                        scores[q_id] = 0.0
                        if progress_callback:
                            progress_callback("warning", f"No marking scheme found for {q_id}")
            else:
                scores[q_id] = 0.0
                if progress_callback:
                    progress_callback("warning", f"Unknown question ID: {q_id}")
        
        return scores
    
    def _calculate_final_score(self, scores: Dict) -> Dict:
        """Calculate final score as percentage dynamically."""
        # Each question is worth 100 points, so total possible = number of questions * 100
        num_questions = len(scores)
        total_possible_points = num_questions * 100
        total_score = sum(scores.values())
        final_score_percentage = (total_score / total_possible_points) * 100 if total_possible_points > 0 else 0
        
        return {
            "Final_Score_Percentage": round(final_score_percentage, 2),
            "total_score": total_score,
            "total_possible": total_possible_points,
            "num_questions": num_questions
        }
    
    def process_multiple_booklets(self, booklet_folder: str, progress_callback: Optional[Callable] = None) -> pd.DataFrame:
        """
        Process multiple student booklets.
        
        Args:
            booklet_folder (str): Path to folder containing booklets
            progress_callback (Callable, optional): Callback for progress updates
        
        Returns:
            pd.DataFrame: Results with Final_Score_Percentage column
        """
        booklet_folder = Path(booklet_folder)
        
        # Get list of valid booklet files
        valid_extensions = (".jpg", ".png", ".jpeg", ".tif", ".pdf")
        booklet_files = [f for f in os.listdir(booklet_folder) 
                        if f.lower().endswith(valid_extensions)]
        
        if not booklet_files:
            if progress_callback:
                progress_callback("error", "No valid student answer booklets found")
            return pd.DataFrame()
        
        if progress_callback:
            progress_callback("started", f"Starting to process {len(booklet_files)} booklets")
        
        results = []
        
        for i, file in enumerate(booklet_files):
            file_path = booklet_folder / file
            
            if progress_callback:
                progress_callback("file_progress", f"Processing {file} ({i+1}/{len(booklet_files)})", 
                                {"current": i+1, "total": len(booklet_files), "file": file})
            
            result = self.process_single_booklet(str(file_path), progress_callback)
            
            if result["status"] == "success":
                # Create result row for DataFrame
                result_row = {"Student": result["Student"]}
                result_row.update(result["scores"])
                result_row["Final_Score_Percentage"] = result["Final_Score_Percentage"]
                results.append(result_row)
        
        if progress_callback:
            progress_callback("finished", f"Completed processing. {len(results)} successful out of {len(booklet_files)}")
        
        return pd.DataFrame(results) if results else pd.DataFrame()


def create_exam_processor_from_files(questions_file: str = "questions.csv",
                                   marking_scheme_file: str = "marking_scheme.csv",
                                   model = None) -> ExamProcessor:
    """
    Create an ExamProcessor instance by loading from files.

    Args:
        questions_file (str): Path to questions CSV
        marking_scheme_file (str): Path to marking scheme CSV
        model: ML model for written answer evaluation. If None, will load default model.

    Returns:
        ExamProcessor: Configured processor instance
    """
    questions, marking_scheme = load_questions_and_marking_scheme(questions_file, marking_scheme_file)

    # Load model if not provided
    if model is None:
        try:
            # Try to import and load the model from examAImarking
            from examAImarking import load_model
            model = load_model()
            logger.info("Loaded model using examAImarking.load_model()")
        except Exception as e:
            logger.warning(f"Could not load model from examAImarking: {e}")
            try:
                # Fallback to default model
                from sentence_transformers import SentenceTransformer
                model = SentenceTransformer("all-MiniLM-L6-v2")
                logger.info("Loaded default SentenceTransformer model")
            except Exception as e2:
                logger.error(f"Could not load any model: {e2}")
                model = None

    return ExamProcessor(questions, marking_scheme, model)
