#!/usr/bin/env python
"""
Launcher script for the Exam AI Marking System.
This script sets up the environment properly before running the Streamlit app.
"""

import os
import sys
import subprocess
import webbrowser
import time
import socket
import argparse

def is_port_in_use(port):
    """Check if a port is already in use"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(('localhost', port)) == 0

def find_available_port(start_port=8501, max_attempts=10):
    """Find an available port starting from start_port"""
    port = start_port
    for _ in range(max_attempts):
        if not is_port_in_use(port):
            return port
        port += 1
    return start_port  # Return the start port if no available port found

def main():
    """Main entry point for the application"""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Run the Exam AI Marking System")
    parser.add_argument("--port", type=int, default=8501, help="Port to run the Streamlit app on")
    args = parser.parse_args()
    
    # Get the path to the app.py file
    app_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "app.py")
    port = args.port
    
    # Set environment variables to disable watching problematic modules
    os.environ["STREAMLIT_WATCH_MODULES"] = "false"
    os.environ["STREAMLIT_DISABLE_WATCHER"] = "true"
    # Add this line to specifically exclude torch modules
    os.environ["STREAMLIT_SERVER_WATCH_EXCLUDE_PATTERNS"] = "torch.*"
    
    # Print startup message
    print("=" * 80)
    print("Starting Exam AI Marking System")
    print("=" * 80)
    print(f"App path: {app_path}")
    print(f"Port: {port}")
    print("Environment variables:")
    print(f"  STREAMLIT_WATCH_MODULES: {os.environ.get('STREAMLIT_WATCH_MODULES', 'Not set')}")
    print(f"  STREAMLIT_DISABLE_WATCHER: {os.environ.get('STREAMLIT_DISABLE_WATCHER', 'Not set')}")
    print("=" * 80)
    
    # Run the Streamlit app with the proper command line arguments
    cmd = [
        sys.executable, "-m", "streamlit", "run", 
        app_path,
        "--server.port", str(port),
        "--server.address", "localhost",
        "--browser.serverPort", str(port),
        "--browser.serverAddress", "localhost",
        "--server.enableCORS", "false",
        "--server.enableXsrfProtection", "false"
    ]
    
    # Start the Streamlit process
    try:
        # Start the process
        process = subprocess.Popen(cmd)
        
        # Wait a moment for the server to start
        time.sleep(2)
        
        # Open the browser
        url = f"http://localhost:{port}"
        print(f"Opening browser at {url}")
        webbrowser.open(url)
        
        # Wait for the process to complete
        process.wait()
        
    except subprocess.CalledProcessError as e:
        print(f"Error running Streamlit app: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("App stopped by user")
        sys.exit(0)
    finally:
        # Make sure to terminate the process if it's still running
        if 'process' in locals() and process.poll() is None:
            process.terminate()

if __name__ == "__main__":
    main()




