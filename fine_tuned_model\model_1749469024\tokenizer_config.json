{"added_tokens_decoder": {"0": {"content": "[PAD]", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "100": {"content": "[UNK]", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "101": {"content": "[CLS]", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "102": {"content": "[SEP]", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "103": {"content": "[MASK]", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}}, "clean_up_tokenization_spaces": false, "cls_token": "[CLS]", "do_basic_tokenize": true, "do_lower_case": true, "extra_special_tokens": {}, "mask_token": "[MASK]", "max_length": 128, "model_max_length": 256, "never_split": null, "pad_to_multiple_of": null, "pad_token": "[PAD]", "pad_token_type_id": 0, "padding_side": "right", "sep_token": "[SEP]", "stride": 0, "strip_accents": null, "tokenize_chinese_chars": true, "tokenizer_class": "<PERSON><PERSON><PERSON><PERSON>", "truncation_side": "right", "truncation_strategy": "longest_first", "unk_token": "[UNK]"}