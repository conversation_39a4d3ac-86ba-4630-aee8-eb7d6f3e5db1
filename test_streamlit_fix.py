#!/usr/bin/env python3
"""
Test script to verify that the Streamlit app now includes Final_Score_Percentage.
"""

import pandas as pd
import sys
import os
from pathlib import Path

# Add current directory to path
sys.path.append('.')

def test_streamlit_result_creation():
    """Test the result creation logic from the Streamlit app."""
    
    print("Testing Streamlit app result creation logic...")
    
    # Simulate the result creation process from the Streamlit app
    # This mimics what happens in the process_student_answers function
    
    # Sample scores for testing
    test_scores = {
        'Q1': 85.5,
        'Q2': 92.3,
        'Q3': 100,
        'Q4': 0,
        'Q5': 100,
        'Q6': 78.2
    }
    
    file = "test_student.png"
    
    # Calculate final score as percentage dynamically (from updated Streamlit code)
    num_questions = len(test_scores)
    total_possible_points = num_questions * 100
    total_score = sum(test_scores.values())
    final_score_percentage = (total_score / total_possible_points) * 100 if total_possible_points > 0 else 0
    
    # Create result row with Final_Score_Percentage (from updated Streamlit code)
    result_row = {"Student": file}
    result_row.update(test_scores)  # Add all question scores
    result_row["Final_Score_Percentage"] = round(final_score_percentage, 2)
    
    print(f"Test scores: {test_scores}")
    print(f"Number of questions: {num_questions}")
    print(f"Total possible points: {total_possible_points}")
    print(f"Total score: {total_score}")
    print(f"Final score percentage: {final_score_percentage:.2f}%")
    print(f"Result row: {result_row}")
    print(f"Result row keys: {list(result_row.keys())}")
    
    # Create DataFrame to test
    results = [result_row]
    df = pd.DataFrame(results)
    
    print(f"\nDataFrame columns: {df.columns.tolist()}")
    print(f"DataFrame:")
    print(df)
    
    # Verify Final_Score_Percentage is present
    if 'Final_Score_Percentage' in df.columns:
        print("\n✅ SUCCESS: Final_Score_Percentage column is present!")
        print(f"Final score value: {df['Final_Score_Percentage'].iloc[0]}%")
        return True
    else:
        print("\n❌ FAILURE: Final_Score_Percentage column is missing!")
        return False

def test_dynamic_calculation():
    """Test dynamic calculation with different numbers of questions."""
    
    print("\n" + "="*60)
    print("TESTING DYNAMIC CALCULATION")
    print("="*60)
    
    test_cases = [
        {"name": "6 Questions", "scores": {'Q1': 80, 'Q2': 90, 'Q3': 100, 'Q4': 70, 'Q5': 85, 'Q6': 95}},
        {"name": "3 Questions", "scores": {'Q1': 80, 'Q2': 90, 'Q3': 100}},
        {"name": "10 Questions", "scores": {f'Q{i}': 85 for i in range(1, 11)}},
    ]
    
    for case in test_cases:
        scores = case["scores"]
        num_questions = len(scores)
        total_possible_points = num_questions * 100
        total_score = sum(scores.values())
        final_score_percentage = (total_score / total_possible_points) * 100
        
        print(f"\n{case['name']}:")
        print(f"  Scores: {scores}")
        print(f"  Total: {total_score}/{total_possible_points}")
        print(f"  Final: {final_score_percentage:.2f}%")

if __name__ == "__main__":
    success = test_streamlit_result_creation()
    test_dynamic_calculation()
    
    if success:
        print("\n🎉 All tests passed! The Streamlit app should now include Final_Score_Percentage.")
    else:
        print("\n❌ Tests failed! There may be an issue with the implementation.")
