#!/usr/bin/env python3
"""
Simple test to verify the core module fix.
"""

from exam_processing_core import create_exam_processor_from_files
import os

def test_core_module():
    print("Testing fixed core module...")
    
    try:
        processor = create_exam_processor_from_files()
        print(f"✅ Processor created successfully")
        print(f"   Model loaded: {processor.model is not None}")
        print(f"   Questions: {len(processor.questions)}")
        print(f"   Marking scheme: {len(processor.marking_scheme)}")
        
        # Test with a file if available
        booklet_folder = "./exam_booklets"
        if os.path.exists(booklet_folder):
            files = [f for f in os.listdir(booklet_folder) if f.lower().endswith((".png", ".jpg", ".jpeg"))]
            if files:
                test_file = os.path.join(booklet_folder, files[0])
                print(f"   Testing with: {files[0]}")
                
                def simple_callback(stage, message, data=None):
                    if stage in ["completed", "error"]:
                        print(f"     {stage}: {message}")
                
                result = processor.process_single_booklet(test_file, simple_callback)
                print(f"   Result status: {result['status']}")
                if result["status"] == "success":
                    print(f"   Final score: {result['Final_Score_Percentage']}%")
                    return True
                else:
                    print(f"   Error: {result.get('error', 'Unknown')}")
                    return False
            else:
                print("   No test files available")
                return True
        else:
            print("   No booklet folder found")
            return True
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_core_module()
    if success:
        print("✅ Core module test passed!")
    else:
        print("❌ Core module test failed!")
