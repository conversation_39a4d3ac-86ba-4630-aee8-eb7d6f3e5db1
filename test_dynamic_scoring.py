#!/usr/bin/env python3
"""
Test script to demonstrate dynamic final score calculation
that works with any number of questions.
"""

import pandas as pd
import sys
import os

def test_dynamic_scoring():
    """Test the dynamic scoring system with different numbers of questions."""
    
    print("=" * 60)
    print("DYNAMIC FINAL SCORE CALCULATION TEST")
    print("=" * 60)
    
    # Test cases with different numbers of questions
    test_cases = [
        {
            "name": "Current System (6 Questions)",
            "questions": ["Q1", "Q2", "Q3", "Q4", "Q5", "Q6"],
            "student_scores": {
                "Student_A": [85, 90, 100, 100, 100, 95],
                "Student_B": [80, 85, 0, 0, 0, 90],
                "Student_C": [70, 75, 100, 100, 100, 80]
            }
        },
        {
            "name": "Extended System (10 Questions)",
            "questions": [f"Q{i}" for i in range(1, 11)],
            "student_scores": {
                "Student_A": [85, 90, 100, 95, 80, 85, 90, 95, 100, 85],
                "Student_B": [75, 80, 0, 0, 0, 70, 75, 80, 85, 90],
                "Student_C": [90, 95, 100, 100, 100, 85, 90, 95, 100, 90]
            }
        },
        {
            "name": "Large System (20 Questions)",
            "questions": [f"Q{i}" for i in range(1, 21)],
            "student_scores": {
                "Student_A": [80] * 20,  # Consistent 80% performance
                "Student_B": [60] * 20,  # Consistent 60% performance
                "Student_C": [95] * 20   # Consistent 95% performance
            }
        },
        {
            "name": "Very Large System (30 Questions)",
            "questions": [f"Q{i}" for i in range(1, 31)],
            "student_scores": {
                "Student_A": [75] * 30,  # Consistent 75% performance
                "Student_B": [50] * 30,  # Consistent 50% performance
                "Student_C": [90] * 30   # Consistent 90% performance
            }
        }
    ]
    
    for case in test_cases:
        print(f"\n{case['name']}")
        print("-" * len(case['name']))
        
        num_questions = len(case['questions'])
        total_possible_points = num_questions * 100
        
        print(f"Number of Questions: {num_questions}")
        print(f"Total Possible Points: {total_possible_points}")
        print(f"Formula: (Sum of Scores / {total_possible_points}) × 100")
        print()
        
        # Calculate scores for each student
        for student_name, scores in case['student_scores'].items():
            total_score = sum(scores)
            final_percentage = (total_score / total_possible_points) * 100
            
            print(f"{student_name}:")
            print(f"  Individual Scores: {scores}")
            print(f"  Total Score: {total_score}")
            print(f"  Final Percentage: {final_percentage:.2f}%")
            print()
    
    print("=" * 60)
    print("KEY BENEFITS OF DYNAMIC SCORING:")
    print("=" * 60)
    print("✅ Works with ANY number of questions (3, 6, 10, 20, 30, etc.)")
    print("✅ Always normalizes to 100% scale for easy comparison")
    print("✅ Maintains consistency across different exam formats")
    print("✅ Automatically adapts to your question structure")
    print("✅ No need to modify code when changing number of questions")
    print()

def show_current_results():
    """Show the current exam results."""
    
    print("=" * 60)
    print("CURRENT EXAM RESULTS")
    print("=" * 60)
    
    try:
        df = pd.read_csv('./exam_results.csv')
        print(df.to_string(index=False))
        
        print(f"\nSummary:")
        print(f"- Number of Questions: {len([col for col in df.columns if col.startswith('Q')])}")
        print(f"- Number of Students: {len(df)}")
        print(f"- Highest Score: {df['Final_Score_Percentage'].max():.2f}%")
        print(f"- Lowest Score: {df['Final_Score_Percentage'].min():.2f}%")
        print(f"- Average Score: {df['Final_Score_Percentage'].mean():.2f}%")
        
    except FileNotFoundError:
        print("No exam results file found. Please run the exam processing first.")

if __name__ == "__main__":
    test_dynamic_scoring()
    print()
    show_current_results()
